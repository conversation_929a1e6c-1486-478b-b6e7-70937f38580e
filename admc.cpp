﻿// anda motion control
#include "admc.h"
#include "ioctrl.h"
#include "adconfig.h"
#include "cmdcode.h"
#include "admc_info.h"
#include "logger_proxy.h"
#include "logger_internal.h"
#include <cstdio> // For snprintf


short MC_Reset(TADMotionConn* handle) //复位
{
    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, RESET);  //0X1200

    short ret = SendCommand(handle, tick);
    CommandUninitial(handle, tick);
    return ret;
    return 0;
}

short MC_SetAxisPrm(TADMotionConn* handle,
                    short          crd,
                    short*         AxisMap,
                    short*         AxisDir,
                    int32_t*       VelMax,
                    int32_t*       AccMax,
                    int32_t*       Positive,
                    int32_t*       Negative)
{
    g_Log(handle, LOG_INFO, "MC_SetAxisPrm: crd=%d", crd);
    INTERNAL_LOG(handle, "MC_SetAxisPrm is start: crd=%d, AxisMap=(%d,%d), AxisDir=(%d,%d), VelMax=(%d,%d), AccMax=(%d,%d), Positive=(%d,%d), Negative=(%d,%d)", 
    crd, AxisMap[0], AxisMap[1],AxisDir[0], AxisDir[1], VelMax[0], VelMax[1], AccMax[0], AccMax[1], Positive[0], Positive[1], Negative[0], Negative[1]);
    if ((crd < 0) || (crd > MAX_CRD))
    {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "MC_SetAxisPrm",
                                "无效的坐标系号 %d", crd);
    }

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, SET_AXIS_PRM);
    Add16ToBuff(handle, tick, crd);
    int i = 0;
    for (i = 0; i < MAX_CRD_AXIS; i++)
    {
        Add16ToBuff(handle, tick, AxisMap[i]);
    }
    for (i = 0; i < MAX_CRD_AXIS; i++)
    {
        Add16ToBuff(handle, tick, AxisDir[i]);
    }
    for (i = 0; i < MAX_CRD_AXIS; i++)
    {
        Add32ToBuff(handle, tick, VelMax[i]);
    }
    for (i = 0; i < MAX_CRD_AXIS; i++)
    {
        Add32ToBuff(handle, tick, AccMax[i]);
    }
    for (i = 0; i < MAX_CRD_AXIS; i++)
    {
        Add32ToBuff(handle, tick, Positive[i]);
    }
    for (i = 0; i < MAX_CRD_AXIS; i++)
    {
        Add32ToBuff(handle, tick, Negative[i]);
    }

    short ret = SendCommand(handle, tick);
    if(ret != CMD_SUCCESS)
    {
        CommandUninitial(handle, tick);
        char commandInfo[256];
        snprintf(commandInfo, sizeof(commandInfo), "crd=%d", crd);
        return LogSendCommandError(handle, ret, "MC_SetAxisPrm", commandInfo);
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_SetAxisPrm is success: crd=%d, AxisMap=(%d,%d), AxisDir=(%d,%d), VelMax=(%d,%d), AccMax=(%d,%d), Positive=(%d,%d), Negative=(%d,%d)", 
    crd, AxisMap[0], AxisMap[1],AxisDir[0], AxisDir[1], VelMax[0], VelMax[1], AccMax[0], AccMax[1], Positive[0], Positive[1], Negative[0], Negative[1]);
    return ret;
}

short MC_AxisEnable(TADMotionConn* handle, short crd, short axis, short operate)
{
    g_Log(handle, LOG_INFO, "MC_AxisEnable: crd=%d, axis=%d, operate=%d", crd, axis, operate);

    if ((axis < 0) || (axis > MAX_AXIS))
    {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "MC_AxisEnable",
                                "无效的轴号 %d", axis);
    }
    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, AXIS_ENABLE);
    Add16ToBuff(handle, tick, crd);
    Add16ToBuff(handle, tick, axis);
    Add16ToBuff(handle, tick, operate);

    short ret = SendCommand(handle, tick);
    if(ret != CMD_SUCCESS)
    {
        CommandUninitial(handle, tick);
        return LogSendCommandError(handle, ret, "MC_AxisEnable",
                                  "crd=%d, axis=%d, operate=%d");
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_AxisEnable is success: crd=%d, axis=%d, operate=%d", crd, axis, operate);
    return ret;
}

short MC_Stop(TADMotionConn* handle, int32_t mask, int32_t option)
{
    g_Log(handle, LOG_INFO, "MC_Stop: crd=%d, option=%d", mask, option);
    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, STOP);
    Add32ToBuff(handle, tick, mask);
    Add32ToBuff(handle, tick, option);

    short ret = SendCommand(handle, tick);
    if(ret != CMD_SUCCESS)
    {
        CommandUninitial(handle, tick);
        char commandInfo[128];
        snprintf(commandInfo, sizeof(commandInfo), "mask=%d, option=%d", mask, option);
        return LogSendCommandError(handle, ret, "MC_Stop", commandInfo);
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_Stop is success: crd=%d, option=%d", mask, option);
    return ret;
}

short MC_RECORD_ZERO_POS(TADMotionConn* handle, short crd) //记录零位置
{
    g_Log(handle, LOG_INFO, "MC_RECORD_ZERO_POS: crd=%d", crd);
    if ((crd < 0) || (crd > MAX_AXIS))
    {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "MC_RECORD_ZERO_POS",
                                "无效的坐标系号 %d", crd);
    }

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, RECORD_ZERO_POS);
    Add16ToBuff(handle, tick, crd);

    short ret = SendCommand(handle, tick);
    if(ret != CMD_SUCCESS)
    {
        CommandUninitial(handle, tick);
        char commandInfo[128];
        snprintf(commandInfo, sizeof(commandInfo), "crd=%d", crd);
        return LogSendCommandError(handle, ret, "MC_RECORD_ZERO_POS", commandInfo);
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_RECORD_ZERO_POS is success: crd=%d", crd);
    return ret;
}

short MC_ZeroPos(TADMotionConn* handle, short crd) //回零指令
{
    g_Log(handle, LOG_INFO, "MC_ZeroPos: Homing initiated for crd=%d", crd);
    if ((crd < 0) || (crd > MAX_CRD))
    {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "MC_ZeroPos",
                                "无效的坐标系号 %d", crd);
    }
    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, ZERO_POS);
    Add16ToBuff(handle, tick, crd);

    short ret = SendCommand(handle, tick);
    if(ret != CMD_SUCCESS)
    {
        CommandUninitial(handle, tick);
        char commandInfo[128];
        snprintf(commandInfo, sizeof(commandInfo), "crd=%d", crd);
        return LogSendCommandError(handle, ret, "MC_ZeroPos", commandInfo);
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_ZeroPos is success: crd=%d", crd);
    return ret;
}

short MC_AxisHome(TADMotionConn* handle, short crd, short axis) //单轴回零指令
{
    g_Log(handle, LOG_INFO, "MC_AxisHome: Homing initiated for crd=%d, axis=%d", crd, axis);
    if ((axis < 0) || (axis > MAX_CRD_AXIS))
    {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "MC_AxisHome",
                                "无效的轴号 %d (坐标系 %d)", axis, crd);
    }
    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, AXIS_HOME);
    Add16ToBuff(handle, tick, crd);
    Add16ToBuff(handle, tick, axis);

    short ret = SendCommand(handle, tick);
    if(ret != CMD_SUCCESS)
    {
        CommandUninitial(handle, tick);
        char commandInfo[128];
        snprintf(commandInfo, sizeof(commandInfo), "crd=%d, axis=%d", crd, axis);
        return LogSendCommandError(handle, ret, "MC_AxisHome", commandInfo);
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_AxisHome is success: crd=%d, axis=%d", crd, axis);
    return ret;
}
short MC_AxisClearAlarm(TADMotionConn* handle, short crd, short axis) //单轴清楚报警
{
    g_Log(handle, LOG_INFO, "MC_AxisClearAlarm: Clearing alarm for crd=%d, axis=%d", crd, axis);
    if ((axis < 0) || (axis > MAX_CRD_AXIS))
    {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "MC_AxisClearAlarm",
                                "无效的轴号 %d (坐标系 %d)", axis, crd);
    }
    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, AXIS_CLEAR_ALARM);
    Add16ToBuff(handle, tick, crd);
    Add16ToBuff(handle, tick, axis);

    short ret = SendCommand(handle, tick);
    if(ret != CMD_SUCCESS)
    {
        CommandUninitial(handle, tick);
        char commandInfo[128];
        snprintf(commandInfo, sizeof(commandInfo), "crd=%d, axis=%d", crd, axis);
        return LogSendCommandError(handle, ret, "MC_AxisClearAlarm", commandInfo);
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_AxisClearAlarm is success: crd=%d, axis=%d", crd, axis);
    return ret;
}
//指令ID：获取错误代码
short MC_GetErrorCode(TADMotionConn* handle,short crd,short axis,unsigned short *ErrorCode)
{
    g_Log(handle, LOG_DEBUG, "MC_GetErrorCode: Getting error code for crd=%d, axis=%d", crd, axis);
    if ((axis < 0) || (axis > MAX_CRD_AXIS))
    {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "MC_GetErrorCode",
                                "无效的轴号 %d (坐标系 %d)", axis, crd);
    }
    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, AXIS_GET_ERRORCODE);
    Add16ToBuff(handle, tick, crd);
    Add16ToBuff(handle, tick, axis);

    short ret = SendCommand(handle, tick);
    if (ret != CMD_SUCCESS)
    {
        CommandUninitial(handle, tick);
        char commandInfo[128];
        snprintf(commandInfo, sizeof(commandInfo), "crd=%d, axis=%d", crd, axis);
        return LogSendCommandError(handle, ret, "MC_GetErrorCode", commandInfo);
    }
    *ErrorCode = 0 ;
    //Get16FromBuff(handle, tick, ErrorCode);
    *ErrorCode = 59800;
    //INTERNAL_LOG(handle, "MC_GetErrorCode: crd=%d, axis=%d, ErrorCode=%d", crd, axis, *ErrorCode);

    CommandUninitial(handle, tick);
    return ret;
}
//点位运动
short MC_GetPrfMode(TADMotionConn* handle, short profile, short type, short* pValue, short count)
{
    short rtn, i;
    g_Log(handle, LOG_INFO, "MC_GetPrfMode: profile=%d, type=%d, count=%d", profile, type, count);
    if ((profile < 0) || (profile > MAX_CRD) || (PROFILE_INFO_MODE != type) || (count > MAX_CRD))
    {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "MC_GetPrfMode",
                                "无效的参数: profile=%d, type=%d, count=%d", profile, type, count);
    }

    if ((profile + count) > (MAX_CRD + 1))
    {
        count = (MAX_CRD + 1) - profile;
    }

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, GET_PROFILE_INFO);
    Add16ToBuff(handle, tick, profile);
    Add16ToBuff(handle, tick, type);
    Add16ToBuff(handle, tick, count);

    rtn = SendCommand(handle, tick);
    if (rtn != CMD_SUCCESS)
    {
        CommandUninitial(handle, tick);
        char commandInfo[128];
        snprintf(commandInfo, sizeof(commandInfo), "profile=%d, type=%d, count=%d", profile, type, count);
        return LogSendCommandError(handle, rtn, "MC_GetPrfMode", commandInfo);
    }

    for (i = 0; i < count; i++)
    {
        Get16FromBuff(handle, tick, &pValue[i]);
    }

    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_GetPrfMode is success: profile=%d, type=%d, count=%d", profile, type, count);
    return CMD_SUCCESS;
}

short MC_CrdPrfTrap(TADMotionConn* handle, short crd) // 006
{
    g_Log(handle, LOG_INFO, "MC_CrdPrfTrap: Set coordinate system to trap mode for crd=%d", crd);
    if ((crd < 0) || (crd > MAX_CRD))
    {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "MC_CrdPrfTrap",
                                "无效的坐标系号 %d", crd);
    }

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, PRF_TRAP);
    Add16ToBuff(handle, tick, crd);

    short ret = SendCommand(handle, tick);
    if(ret != CMD_SUCCESS)
    {
        CommandUninitial(handle, tick);
        char commandInfo[128];
        snprintf(commandInfo, sizeof(commandInfo), "crd=%d", crd);
        return LogSendCommandError(handle, ret, "MC_CrdPrfTrap", commandInfo);
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_CrdPrfTrap is success: crd=%d", crd);
    return ret;
}
short MC_SetCrdTrapPrm(TADMotionConn* handle, short crd, TTrapPrm* pPrm)
{
    g_Log(handle, LOG_INFO, "MC_SetCrdTrapPrm: crd=%d, velMax=%.2f, acc=%.2f, rat=%d", crd, pPrm->velMax, pPrm->acc, pPrm->rat);
    if ((crd < 0) || (crd > MAX_CRD))
    {
        g_Log(handle, LOG_ERROR, "MC_SetCrdTrapPrm: Invalid crd number %d", crd);
        return CMD_API_ERROR_OUT_RANGE;
    }

    if (nullptr == pPrm)
    {
        g_Log(handle, LOG_ERROR, "MC_SetCrdTrapPrm: pPrm is null for crd=%d", crd);
        return CMD_API_ERROR_POINTER;
    }

    if (pPrm->acc <= 0)
    {
        g_Log(handle, LOG_ERROR, "MC_SetCrdTrapPrm: Invalid acc value %.2f for crd=%d", pPrm->acc, crd);
        return CMD_API_ERROR_PRM;
    }

    if (pPrm->velMax < 0)
    {
        g_Log(handle, LOG_ERROR, "MC_SetCrdTrapPrm: Invalid velMax value %.2f for crd=%d", pPrm->velMax, crd);
        return CMD_API_ERROR_PRM;
    }

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, SET_TRAP_PRM);
    Add16ToBuff(handle, tick, crd);

    for (int i = 0; i < MAX_PROFILE; i++)
    {
        Add64Fix16ToBuff(handle, tick, pPrm->StartPos[i]);
    }

    for (int i = 0; i < MAX_PROFILE; i++)
    {
        Add64Fix16ToBuff(handle, tick, pPrm->posTarget[i]);
    }

    Add32Fix16ToBuff(handle, tick, pPrm->velMax);

    Add16ToBuff(handle, tick, pPrm->rat);
    Add32Fix16ToBuff(handle, tick, pPrm->acc);

    short ret = SendCommand(handle, tick);
    if(ret != CMD_SUCCESS)
    {
        g_Log(handle, LOG_ERROR, "MC_SetCrdTrapPrm: SendCommand failed, ret=%d", ret);
        return ret;
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_SetCrdTrapPrm is success: crd=%d,startPos[0]=%.2f,startPos[1]=%.2f,posTarget[0]=%.2f,posTarget[1]=%.2f, velMax=%.2f, acc=%.2f, rat=%d", crd, 
    pPrm->StartPos[0], pPrm->StartPos[1], pPrm->posTarget[0], pPrm->posTarget[1], pPrm->velMax, pPrm->acc, pPrm->rat);
    return ret;
}
short MC_CrdTrapUpdate(TADMotionConn* handle, short crd) //启动更新
{
    g_Log(handle, LOG_INFO, "MC_CrdTrapUpdate: crd=%d", crd);
    if ((crd < 0) || (crd > MAX_CRD))
    {
        g_Log(handle, LOG_ERROR, "MC_CrdTrapUpdate: Invalid crd number %d", crd);
        return CMD_API_ERROR_OUT_RANGE;
    }

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, UPDATE);
    Add16ToBuff(handle, tick, crd);
    short ret = SendCommand(handle, tick);
    if(ret != CMD_SUCCESS)
    {
        g_Log(handle, LOG_ERROR, "MC_CrdTrapUpdate: SendCommand failed, ret=%d", ret);
        return ret;
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_CrdTrapUpdate is success: crd=%d", crd);
    return ret;
}
// Aixs Trap

short MC_AxisPrfTrap(TADMotionConn* handle, short crd, short axis) // 006
{
    g_Log(handle, LOG_INFO, "MC_AxisPrfTrap: Set axis to trap mode for crd=%d, axis=%d", crd, axis);
    if ((crd < 0) || (crd > MAX_CRD))
    {
        g_Log(handle, LOG_ERROR, "MC_AxisPrfTrap: Invalid crd number %d", crd);
        return CMD_API_ERROR_OUT_RANGE;
    }
    if ((axis < 0) || (axis > MAX_CRD_AXIS))
    {
        g_Log(handle, LOG_ERROR, "MC_AxisPrfTrap: Invalid axis number %d", axis);
        return CMD_API_ERROR_OUT_RANGE;
    }

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, PRF_AxisTRAP);
    Add16ToBuff(handle, tick, crd);
    Add16ToBuff(handle, tick, axis);

    short ret = SendCommand(handle, tick);
    if(ret != CMD_SUCCESS)
    {
        g_Log(handle, LOG_ERROR, "MC_AxisPrfTrap: SendCommand failed, ret=%d", ret);
        return ret;
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_AxisPrfTrap is success: crd=%d, axis=%d", crd, axis);
    return ret;
}
short MC_SetAixsTrapPrm(TADMotionConn* handle, short crd, short axis, double IncrPos, double velMax, double acc, short rat)
{
    g_Log(handle, LOG_INFO, "MC_SetAixsTrapPrm: crd=%d, axis=%d, IncrPos=%.2f, velMax=%.2f, acc=%.2f, rat=%d", crd, axis, IncrPos, velMax, acc, rat);
    if ((crd < 0) || (crd > MAX_CRD))
    {
        g_Log(handle, LOG_ERROR, "MC_SetAixsTrapPrm: Invalid crd number %d", crd);
        return CMD_API_ERROR_OUT_RANGE;
    }
    if ((axis < 0) || (axis > MAX_CRD_AXIS))
    {
        g_Log(handle, LOG_ERROR, "MC_SetAixsTrapPrm: Invalid axis number %d", axis);
        return CMD_API_ERROR_OUT_RANGE;
    }

    if (velMax <= 0)
    {
        g_Log(handle, LOG_ERROR, "MC_SetAixsTrapPrm: Invalid velMax value %.2f", velMax);
        return CMD_API_ERROR_PRM;
    }

    if (acc <= 0)
    {
        g_Log(handle, LOG_ERROR, "MC_SetAixsTrapPrm: Invalid acc value %.2f", acc);
        return CMD_API_ERROR_PRM;
    }

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, SET_AxisTRAP_PRM);
    Add16ToBuff(handle, tick, crd);
    Add16ToBuff(handle, tick, axis);

    Add64Fix16ToBuff(handle, tick, IncrPos);
    Add32Fix16ToBuff(handle, tick, velMax);

    Add16ToBuff(handle, tick, rat);
    Add32Fix16ToBuff(handle, tick, acc);

    short ret = SendCommand(handle, tick);
    if(ret != CMD_SUCCESS)
    {
        g_Log(handle, LOG_ERROR, "MC_SetAixsTrapPrm: SendCommand failed, ret=%d", ret);
        return ret;
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_SetAixsTrapPrm is success: crd=%d, axis=%d, IncrPos=%.2f, velMax=%.2f, acc=%.2f, rat=%d", crd, axis, IncrPos, velMax, acc, rat);
    return ret;
}
short MC_AxisTrapUpdate(TADMotionConn* handle, short crd) //启动更新
{
    g_Log(handle, LOG_INFO, "MC_AxisTrapUpdate: crd=%d", crd);
    if ((crd < 0) || (crd > MAX_CRD))
    {
        g_Log(handle, LOG_ERROR, "MC_AxisTrapUpdate: Invalid crd number %d", crd);
        return CMD_API_ERROR_OUT_RANGE;
    }

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, AxisTRAP_UPDATE);
    Add16ToBuff(handle, tick, crd);

    short ret = SendCommand(handle, tick);
    if(ret != CMD_SUCCESS)
    {
        g_Log(handle, LOG_ERROR, "MC_AxisTrapUpdate: SendCommand failed, ret=%d", ret);
        return ret;
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_AxisTrapUpdate is success: crd=%d", crd);
    return ret;
}

// JOG 模式
short SetJogMode(TADMotionConn* handle, short crd)
{
    g_Log(handle, LOG_INFO, "SetJogMode: Set coordinate system to JOG mode for crd=%d", crd);
    if ((crd < 0) || (crd > MAX_CRD))
    {
        g_Log(handle, LOG_ERROR, "SetJogMode: Invalid crd number %d", crd);
        return CMD_API_ERROR_OUT_RANGE;
    }

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, PRF_JOG);
    Add16ToBuff(handle, tick, crd);

    short ret = SendCommand(handle, tick);
    if(ret != CMD_SUCCESS)
    {
        g_Log(handle, LOG_ERROR, "SetJogMode: SendCommand failed, ret=%d", ret);
        return ret;
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "SetJogMode is success: crd=%d", crd);
    return ret;
}

short MC_SetJogPrm(TADMotionConn* handle, short crd, int32_t Maxvel, int32_t acc, int32_t dec, int32_t rate)
{
    g_Log(handle, LOG_INFO, "MC_SetJogPrm: crd=%d, Maxvel=%d, acc=%d, dec=%d, rate=%d", crd, Maxvel, acc, dec, rate);
    if ((crd < 0) || (crd > MAX_CRD))
    {
        g_Log(handle, LOG_ERROR, "MC_SetJogPrm: Invalid crd number %d", crd);
        return CMD_API_ERROR_OUT_RANGE;
    }
    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, SET_JOG_PRM);

    Add16ToBuff(handle, tick, crd);
    Add32ToBuff(handle, tick, acc);
    Add32ToBuff(handle, tick, Maxvel);
    Add32ToBuff(handle, tick, rate);

    short ret = SendCommand(handle, tick);
    if(ret != CMD_SUCCESS)
    {
        g_Log(handle, LOG_ERROR, "MC_SetJogPrm: SendCommand failed, ret=%d", ret);
        return ret;
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_SetJogPrm is success: crd=%d, Maxvel=%d, acc=%d, dec=%d, rate=%d", crd, Maxvel, acc, dec, rate);
    return ret;
}

short MC_JOGUpdata(TADMotionConn* handle, short crd, short mask) // mask = 1 2 3 4 x+ x- y+ y- 0:stop
{
    g_Log(handle, LOG_INFO, "MC_JOGUpdata: crd=%d, mask=%d", crd, mask);
    if ((crd < 0) || (crd > MAX_CRD))
    {
        g_Log(handle, LOG_ERROR, "MC_JOGUpdata: Invalid crd number %d", crd);
        return CMD_API_ERROR_OUT_RANGE;
    }
    if ((mask < 0) || (mask > 5))
    {
        g_Log(handle, LOG_ERROR, "MC_JOGUpdata: Invalid mask value %d", mask);
        return CMD_API_ERROR_PRM;
    }

    unsigned short tick = GetIndex(handle);
    CommandInitial(handle, tick, JOGUPDATE);
    Add16ToBuff(handle, tick, crd);
    Add16ToBuff(handle, tick, mask);

    short ret = SendCommand(handle, tick);
    if(ret != CMD_SUCCESS)
    {
        g_Log(handle, LOG_ERROR, "MC_JOGUpdata: SendCommand failed, ret=%d", ret);
        return ret;
    }
    CommandUninitial(handle, tick);
    INTERNAL_LOG(handle, "MC_JOGUpdata is success: crd=%d, mask=%d", crd, mask);
    return ret;
}
