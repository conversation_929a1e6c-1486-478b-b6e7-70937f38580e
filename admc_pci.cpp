#include "admc_pci.h"
#include "cmdcode.h"
#include "ioctrl.h"
#include "card.h"
#include "admc.h"
#include "ADMotionPackage.h"
#include "CardData.h"
#include "motion_error_codes.h"  // 使用新的错误码头文件
#include "motion_conn.h"
#include "logger_proxy.h"        // 添加日志代理
#include "logger_internal.h"     // 添加内部日志
#include <map>
#include <mutex>
#include <memory>
#include <cstdio>               // For snprintf

// 全局映射，用于从C句柄找到C++包装器对象
std::map<TADMotionConn*, std::shared_ptr<MotionConn>> g_handleMap;
std::mutex g_handleMapMutex;

// 注意：CMD_API_ERROR_INVALID_HANDLE 现在在 motion_error_codes.h 中定义

short convert_axis_to_crd(short axis, short& crd, short& axisTemp)
{
    if (axis < 0) {
        // 注意：这是内部函数，不能使用g_Log，因为没有handle参数
        return CMD_API_ERROR_OUT_RANGE;
    }
    crd = axis / 2;
    axisTemp = axis % 2;
    return CMD_SUCCESS;
}
//handle指针校验函数
bool __stdcall API_CheckHandle(TADMotionConn* handle) {
    if (handle == nullptr) {
        return false;
    }
    return true;
}   


TADMotionConn* __stdcall API_CreateBoard() {
    TADMotionConn* handle = nullptr;
    try
    {
        handle = new TADMotionConn();
        auto connWrapper = std::make_shared<MotionConn>(handle);

        std::lock_guard<std::mutex> lock(g_handleMapMutex);
        g_handleMap[handle] = connWrapper;

        return handle;
    }
    catch (...)
    {
        if (handle) {
            delete handle;
        }
        return nullptr;
    }
}

void __stdcall API_DeleteBoard(TADMotionConn* handle) {
    if (handle != nullptr) {
        std::lock_guard<std::mutex> lock(g_handleMapMutex);
        g_handleMap.erase(handle);
        delete handle;
    }
}

short __stdcall API_OpenBoard(TADMotionConn* handle, const char* ip, int port) //打开卡
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }

    short ret = MC_Open(handle, ip, port);
    return ret;
}

short __stdcall API_CloseBoard(TADMotionConn* handle) //关闭卡
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    short ret = MC_Close(handle);
    return ret;
}

short __stdcall API_ResetBoard(TADMotionConn* handle)
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }

    // 初始化所有坐标系
    for (int i = 0; i < MAX_CRD; i++)
    {
        InitCrd(handle, i);
        INTERNAL_LOG(handle, "API_ResetBoard: 初始化坐标系 %d", i);
    }

    short ret = MC_Reset(handle);
    return ret;
}

short __stdcall API_SetAxisPrm(TADMotionConn* handle,
                               short          crd,
                               short*         AxisMap,
                               short*         AxisDir,
                               int32_t*       VelMax,
                               int32_t*       AccMax,
                               int32_t*       Positive,
                               int32_t*       Negative) //设置轴参数
{
    g_Log(handle, LOG_INFO, "API_SetAxisPrm: 设置轴参数 crd=%d", crd);

    if (API_CheckHandle(handle) == false) {
        g_Log(nullptr, LOG_ERROR, "API_SetAxisPrm: 无效的句柄");
        return CMD_API_ERROR_INVALID_HANDLE;
    }

    // 检查指针参数
    if (!AxisMap || !AxisDir || !VelMax || !AccMax || !Positive || !Negative) {
        return LogAndReturnError(handle, CMD_API_ERROR_POINTER, "API_SetAxisPrm",
                                "参数指针为空");
    }

    short ret = MC_SetAxisPrm(handle, crd, AxisMap, AxisDir, VelMax, AccMax, Positive, Negative);
    if (ret == CMD_SUCCESS) {
        INTERNAL_LOG(handle, "API_SetAxisPrm: 成功设置轴参数");
    }
    return ret;
}

short __stdcall API_AxisOn(TADMotionConn* handle, short axis) //使能开
{
    g_Log(handle, LOG_INFO, "API_AxisOn: 使能轴 %d", axis);

    if (API_CheckHandle(handle) == false) {
        g_Log(nullptr, LOG_ERROR, "API_AxisOn: 无效的句柄");
        return CMD_API_ERROR_INVALID_HANDLE;
    }

    short crd, axisTemp;
    short rtn = convert_axis_to_crd(axis, crd, axisTemp);
    if (rtn != CMD_SUCCESS) {
        return LogAndReturnError(handle, rtn, "API_AxisOn",
                                "轴号转换失败: axis=%d", axis);
    }

    short ret = MC_AxisEnable(handle, crd, axisTemp, 1);
    if (ret == CMD_SUCCESS) {
        INTERNAL_LOG(handle, "API_AxisOn: 成功使能轴 %d (crd=%d, axisTemp=%d)", axis, crd, axisTemp);
    }
    return ret;
}

short __stdcall API_AxisOff(TADMotionConn* handle, short axis) //使能关
{
    g_Log(handle, LOG_INFO, "API_AxisOff: 禁用轴 %d", axis);

    if (API_CheckHandle(handle) == false) {
        g_Log(nullptr, LOG_ERROR, "API_AxisOff: 无效的句柄");
        return CMD_API_ERROR_INVALID_HANDLE;
    }

    short crd, axisTemp;
    short rtn = convert_axis_to_crd(axis, crd, axisTemp);
    if (rtn != CMD_SUCCESS) {
        return LogAndReturnError(handle, rtn, "API_AxisOff",
                                "轴号转换失败: axis=%d", axis);
    }

    short ret = MC_AxisEnable(handle, crd, axisTemp, 0);
    if (ret == CMD_SUCCESS) {
        INTERNAL_LOG(handle, "API_AxisOff: 成功禁用轴 %d (crd=%d, axisTemp=%d)", axis, crd, axisTemp);
    }
    return ret;
}

// JOG
short __stdcall API_SetJogMode(TADMotionConn* handle, short crd) //设置jog模式
{
    g_Log(handle, LOG_INFO, "API_SetJogMode: 设置JOG模式 crd=%d", crd);

    if (API_CheckHandle(handle) == false) {
        g_Log(nullptr, LOG_ERROR, "API_SetJogMode: 无效的句柄");
        return CMD_API_ERROR_INVALID_HANDLE;
    }

    short ret = SetJogMode(handle, crd);
    if (ret == CMD_SUCCESS) {
        INTERNAL_LOG(handle, "API_SetJogMode: 成功设置JOG模式");
    } else {
        g_Log(handle, LOG_ERROR, "API_SetJogMode: 设置JOG模式失败，错误码=%d", ret);
    }
    return ret;
}

short __stdcall API_SetJogPrm(TADMotionConn* handle, short crd, int32_t Maxvel, int32_t acc, int32_t dec, int32_t rate) //设置jog参数
{
    g_Log(handle, LOG_INFO, "API_SetJogPrm: 设置JOG参数 crd=%d, vel=%d, acc=%d, dec=%d, rate=%d",
          crd, Maxvel, acc, dec, rate);

    if (API_CheckHandle(handle) == false) {
        g_Log(nullptr, LOG_ERROR, "API_SetJogPrm: 无效的句柄");
        return CMD_API_ERROR_INVALID_HANDLE;
    }

    short rtn = MC_SetJogPrm(handle, crd, Maxvel, acc, dec, rate);
    if (rtn != CMD_SUCCESS) {
        g_Log(handle, LOG_ERROR, "API_SetJogPrm: 设置JOG参数失败，错误码=%d", rtn);
        return rtn;
    }

    INTERNAL_LOG(handle, "API_SetJogPrm: 成功设置JOG参数");
    return CMD_SUCCESS;
}


short __stdcall API_JogUpdate(TADMotionConn* handle, short axis, short dir)
{
    g_Log(handle, LOG_DEBUG, "API_JogUpdate: 更新JOG运动 axis=%d, dir=%d", axis, dir);

    if (API_CheckHandle(handle) == false) {
        g_Log(nullptr, LOG_ERROR, "API_JogUpdate: 无效的句柄");
        return CMD_API_ERROR_INVALID_HANDLE;
    }

    short mask = 0;
    short crd, axisTemp;
    short rtn = convert_axis_to_crd(axis, crd, axisTemp);
    if (rtn != CMD_SUCCESS) {
        return LogAndReturnError(handle, rtn, "API_JogUpdate",
                                "轴号转换失败: axis=%d", axis);
    }

    if (dir == 0) // 方向为0时，表示停止当前jog运动
    {
        return MC_JOGUpdata(handle, crd, mask);
    }

    if ((axisTemp == 0) && (dir == -1))
    {
        mask = 2;
    }
    else if ((axisTemp == 0) && (dir == 1))
    {
        mask = 1;
    }
    else if ((axisTemp == 1) && (dir == -1))
    {
        mask = 4;
    }
    else if ((axisTemp == 1) && (dir == 1))
    {
        mask = 3;
    }
    else
    {
        return AXIS_INFO_ERROR;
    }
    return MC_JOGUpdata(handle, crd, mask);
}

short __stdcall API_SetCrdTrapMode(TADMotionConn* handle, short crd) //设置点位模式
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_CrdPrfTrap(handle, crd);
}


short __stdcall API_SetCrdTrapPrm(TADMotionConn* handle, short crd,
double pos_Target[2], double velMax, double acc, short rat) //设置点位参数
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    double start_Pos[2] = {0,0};
    API_GetCrdPos(handle,crd, start_Pos);
    TTrapPrm pPrm;

    pPrm.StartPos[0] = start_Pos[0];
    pPrm.StartPos[1] = start_Pos[1];
    pPrm.posTarget[0] = pos_Target[0];
    pPrm.posTarget[1] = pos_Target[1];
    pPrm.velMax = velMax;
    pPrm.acc = acc;
    pPrm.rat = rat;
    return MC_SetCrdTrapPrm(handle, crd, &pPrm);
}

short __stdcall API_CrdTrapUpdate(TADMotionConn* handle, short crd) //更新
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_CrdTrapUpdate(handle, crd);
}



short __stdcall API_SetAxisTrapMode(TADMotionConn* handle, short axis) //设置点位模式
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    short crd, axisTemp;
    short rtn = convert_axis_to_crd(axis, crd, axisTemp);
    if (rtn != CMD_SUCCESS)
        return rtn;
    return MC_AxisPrfTrap(handle, crd, axisTemp);
}
short __stdcall API_SetAxisTrapPrm(TADMotionConn* handle, short axis, double IncrPos, double velMax, double acc, short rat) //设置点位参数
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    short crd, axisTemp;
    short rtn = convert_axis_to_crd(axis, crd, axisTemp);
    if (rtn != CMD_SUCCESS)
        return rtn;
    return MC_SetAixsTrapPrm(handle, crd, axisTemp, IncrPos, velMax, acc, rat);
}
short __stdcall API_AxisTrapUpdate(TADMotionConn* handle, short crd) //更新
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_AxisTrapUpdate(handle, crd);
}


short __stdcall API_GoHome(TADMotionConn* handle, short crd) //更新
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_ZeroPos(handle, crd);
}
short __stdcall API_AxisGoHome(TADMotionConn* handle, short axis) //更新
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    short crd, axisTemp;    
    short rtn = convert_axis_to_crd(axis, crd, axisTemp);   
    if (rtn != CMD_SUCCESS)
        return rtn;
    return MC_AxisHome(handle, crd, axisTemp);
}
short __stdcall API_AxisClearAlarm(TADMotionConn* handle, short axis) //更新
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    short crd, axisTemp;    
    short rtn = convert_axis_to_crd(axis, crd, axisTemp);   
    if (rtn != CMD_SUCCESS)
        return rtn;
    return MC_AxisClearAlarm(handle, crd, axisTemp);
}
short __stdcall API_GetErrorCode(TADMotionConn* handle, short axis, unsigned short* ErrorCode)
{
    g_Log(handle, LOG_DEBUG, "API_GetErrorCode: 获取轴错误码 axis=%d", axis);

    if (API_CheckHandle(handle) == false) {
        g_Log(nullptr, LOG_ERROR, "API_GetErrorCode: 无效的句柄");
        return CMD_API_ERROR_INVALID_HANDLE;
    }

    if (!ErrorCode) {
        char commandInfo[256];
        snprintf(commandInfo, sizeof(commandInfo), "axis=%d", axis);
        g_Log(handle, LOG_ERROR, "API_GetErrorCode: 无效的句柄");
        return CMD_API_ERROR_POINTER;
    }

    short crd, axisTemp;
    short rtn = convert_axis_to_crd(axis, crd, axisTemp);
    if (rtn != CMD_SUCCESS) {
        g_Log(handle, LOG_ERROR, "API_GetErrorCode: 轴号转换失败: axis=%d", axis);
    }

    short ret = MC_GetErrorCode(handle, crd, axisTemp, ErrorCode);
    if (ret == CMD_SUCCESS) {
        char commandInfo[256];
        snprintf(commandInfo, sizeof(commandInfo), "axis=%d", axis);
        LogAndReturnError(handle, *ErrorCode, "API_GetErrorCode",
                                 commandInfo);
    }
    return ret;
}
//	插补运动
short __stdcall API_SetCrdPrm(TADMotionConn* handle, short crd, double synVelMax, double synAccMax) //设置坐标系参数
{
    g_Log(handle, LOG_INFO, "API_SetCrdPrm: 设置坐标系参数 crd=%d, vel=%.2f, acc=%.2f",
          crd, synVelMax, synAccMax);

    if (API_CheckHandle(handle) == false) {
        g_Log(nullptr, LOG_ERROR, "API_SetCrdPrm: 无效的句柄");
        return CMD_API_ERROR_INVALID_HANDLE;
    }

    if (synVelMax <= 0 || synAccMax <= 0) {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "API_SetCrdPrm",
                                "无效的参数: vel=%.2f, acc=%.2f", synVelMax, synAccMax);
    }

    TCrdPrm pCrdPrm;
    pCrdPrm.synVelMax = synVelMax;
    pCrdPrm.synAccMax = synAccMax;
    pCrdPrm.setOriginFlag = 0;
    for (int i = 0; i < MAX_PROFILE; i++)
    {
        pCrdPrm.originPos[i] = 0;
    }

    short ret = MC_SetCrdPrm(handle, crd, &pCrdPrm);
    if (ret == CMD_SUCCESS) {
        INTERNAL_LOG(handle, "API_SetCrdPrm: 成功设置坐标系参数");
    }
    return ret;
}
short __stdcall API_SendCrdData(TADMotionConn* handle, short crd, TCrdData* pCrdData) //发送坐标系数据
{
    g_Log(handle, LOG_DEBUG, "API_SendCrdData: 发送坐标系数据 crd=%d", crd);

    if (API_CheckHandle(handle) == false) {
        g_Log(nullptr, LOG_ERROR, "API_SendCrdData: 无效的句柄");
        return CMD_API_ERROR_INVALID_HANDLE;
    }

    if (!pCrdData) {
        return LogAndReturnError(handle, CMD_API_ERROR_POINTER, "API_SendCrdData",
                                "坐标系数据指针为空");
    }

    short ret = SendCrdData(handle, crd, pCrdData);
    if (ret == CMD_SUCCESS) {
        INTERNAL_LOG(handle, "API_SendCrdData: 成功发送坐标系数据");
    }
    return ret;
}

short __stdcall API_CrdStart(TADMotionConn* handle, short crd) //坐标插补开始
{
    g_Log(handle, LOG_INFO, "API_CrdStart: 启动坐标插补 crd=%d", crd);

    if (API_CheckHandle(handle) == false) {
        g_Log(nullptr, LOG_ERROR, "API_CrdStart: 无效的句柄");
        return CMD_API_ERROR_INVALID_HANDLE;
    }

    short ret = MC_CrdStart(handle, crd);
    if (ret == CMD_SUCCESS) {
        g_Log(handle, LOG_INFO, "API_CrdStart: 成功启动坐标插补");
    } else {
        g_Log(handle, LOG_ERROR, "API_CrdStart: 启动坐标插补失败，错误码=%d", ret);
    }
    return ret;
}

short __stdcall API_CrdStop(TADMotionConn* handle, short crd) //坐标插补停止
{
    g_Log(handle, LOG_INFO, "API_CrdStop: 停止坐标插补 crd=%d", crd);

    if (API_CheckHandle(handle) == false) {
        g_Log(nullptr, LOG_ERROR, "API_CrdStop: 无效的句柄");
        return CMD_API_ERROR_INVALID_HANDLE;
    }

    short ret = MC_CrdStop(handle, crd);
    if (ret == CMD_SUCCESS) {
        g_Log(handle, LOG_INFO, "API_CrdStop: 成功停止坐标插补");
    } else {
        g_Log(handle, LOG_ERROR, "API_CrdStop: 停止坐标插补失败，错误码=%d", ret);
    }
    return ret;
}

short __stdcall API_CrdPause(TADMotionConn* handle, short crd) //坐标插补暂停
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_CrdPause(handle, crd);
}



//直线  圆弧操作
short __stdcall API_Ln(TADMotionConn* handle, short crd, int32_t x, int32_t y, double synVel, double synAcc)
{
    short  velEnd = 0;
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_CrdLn(handle, API_CMD_LN_XY, false, crd, x, y, 0, 0, 0, synVel, synAcc, velEnd);
}


short __stdcall API_ArcXY_3point(TADMotionConn* handle,
                                 short          crd,
                                 int32_t*       p1,
                                 int32_t*       p2,
                                 int32_t*       p3,
                                 double         synVel,
                                 double         synAcc)
{
    short  velEnd = 0;
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    int32_t MC_p1[3] = {0,0,0};
    int32_t MC_p2[3] = {0,0,0};
    int32_t MC_p3[3] = {0,0,0};
    for(short i=0;i<2;i++)
    {
        MC_p1[i] = p1[i];   
        MC_p2[i] = p2[i];
        MC_p3[i] = p3[i];
    }

    return MC_CrdArc3Point(handle, API_CMD_ARC_XY, crd, MC_p1, MC_p2, MC_p3,  synVel, synAcc, velEnd);
}

short __stdcall API_ArcXYR(TADMotionConn* handle,
                           short          crd,
                           int32_t        x,
                           int32_t        y,
                           double         radius,
                           short          circleDir,
                           double         synVel,
                           double         synAcc)
{
    short  velEnd = 0;
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_CrdArcR(handle, API_CMD_ARC_XY, crd, x, y, 0, radius, circleDir, synVel, synAcc, velEnd);
}
short __stdcall API_ArcXYC(TADMotionConn* handle,
                           short          crd,
                           int32_t        x,
                           int32_t        y,
                           double         xCenter,
                           double         yCenter,
                           short          circleDir,
                           double         synVel,
                           double         synAcc)
{
    short  velEnd = 0;
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_CrdArcC(handle, API_CMD_ARC_XY, crd, x, y, 0, xCenter, yCenter, 0, circleDir, synVel, synAcc, velEnd);
}

short __stdcall API_InitLookAhead(TADMotionConn* handle, short crd, double accMax, short count)
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_InitLookAhead(handle, crd, accMax, handle->CardData->lockData, count);
}
short __stdcall API_CloseLookAhead(TADMotionConn* handle, short crd)
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_CloseLookAhead(handle, crd);
}

short __stdcall API_GetCrdPos(TADMotionConn* handle, short crd, double* pPos)
{
    short count = 2; //获取XY
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_GetCrdPos(handle, crd, pPos, count);
}

short __stdcall API_GetAixsPos(TADMotionConn* handle, short axis, double& pPos)
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    if (axis < 0)
        return CMD_API_ERROR_OUT_RANGE;
    short  crd, axisTemp;    
    short rtn = convert_axis_to_crd(axis, crd, axisTemp);   
    if (rtn != CMD_SUCCESS)
        return rtn;
    double Pos[2];

    rtn = MC_GetCrdPos(handle, crd, Pos, 2);
    if (CMD_SUCCESS == rtn)
    {
        pPos = Pos[axisTemp];
        return CMD_SUCCESS;
    }
    else
    {
        return rtn;
    }
}

short  API_GetCmdCrdPos(TADMotionConn* handle, short crd, double* pPos)
{
        short count = 2; //获取XY
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_GetfbCrdPos(handle, crd, pPos, count);
}

short __stdcall API_GetCmdAixsPos(TADMotionConn* handle, short axis, double& pPos)
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    if (axis < 0)
        return CMD_API_ERROR_OUT_RANGE;
    short  crd, axisTemp;    
    short rtn = convert_axis_to_crd(axis, crd, axisTemp);   
    if (rtn != CMD_SUCCESS)
        return rtn;
    double Pos[2];

    rtn = MC_GetfbCrdPos(handle, crd, Pos, 2);
    if (CMD_SUCCESS == rtn)
    {
        pPos = Pos[axisTemp];
        return CMD_SUCCESS;
    }
    else
    {
        return rtn;
    }
}

short __stdcall API_GetAxisStatus(TADMotionConn* handle, short axis, short& Sts) { 
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_GetAxisStatus(handle, axis, Sts); 
}

short __stdcall API_SetDeviceOutput(TADMotionConn* handle, int* deviceOutput)
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_SetDeviceOutput(handle, deviceOutput);
}

short __stdcall API_GetDeviceOutput(TADMotionConn* handle, int32_t* deviceOutput)
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_GetDeviceOutput(handle, deviceOutput);
}

short __stdcall API_GetDeviceInput(TADMotionConn* handle, int32_t* deviceInput)
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_GetDeviceInput(handle, deviceInput);
}


short __stdcall API_DebugModelOption(TADMotionConn* handle, bool status) { 
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_DebugModelOption(handle, status); 
}


/*
    error:      冗余误差     单位： 0.001mm  编码器单位
    frontTime： 提前开阀时间  单位：  ms
*/
short __stdcall API_PosCmpEnable(TADMotionConn* handle, short crd, bool bEnable, short error, short frontTime) //// 是否开启位置比较功能
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_PosCmpEnable(handle, crd, bEnable, error, frontTime);
}

short __stdcall API_SetPosCmpPoint(
    TADMotionConn* handle, short crd, short seg, double pnt[10240][2], int nPntSize) ////  传输要参与位置比较的点位
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_SetPosCmpPoint(handle, crd, seg, pnt, nPntSize);
}

short __stdcall API_ClearCmpPoint(TADMotionConn* handle, short crd) ////    清理点位
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return  MC_ClearCmpPoint(handle, crd);
}

/*
    short            outNum :
    short            outtype  ： 输出类型  0 持续  1 脉冲
    short           hlTime_ms ： 高电平时间
    short           duty_ms  ：  总时间
*/

short __stdcall API_SetPosCmpOutp(TADMotionConn* handle,
                                  short          crd,
                                  short            outNum,
                                  short            outtype,
                                  long         hlTime_ms,
                                  long         duty_ms)
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return  MC_SetPosCmpOutp(handle, crd, outNum, outtype, hlTime_ms, duty_ms);
}



//io_num = {0,1} IO号
//period ：周期时间 us
//duty   ：高点平时间  0=<duty<=period
short __stdcall API_SetSpeedIOParam(TADMotionConn* handle, short io_num, short duty, short period)
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_SetSpeedIOParam(handle, io_num, duty, period);//duty = 10 ,period = 20;
}
//io_num = {0,1} IO号   switch_state：IO开关 = {0，1}
short __stdcall API_SetSpeedIOState(TADMotionConn* handle, short io_num, short switch_state)
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_SetSpeedIOState(handle, io_num, switch_state);
}

short __stdcall API_SetIOPluseEnable(TADMotionConn* handle,short crd, short io_num, short IO_Enable)
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_SetIOPluseEnable(handle,crd,io_num,IO_Enable);
}
short __stdcall API_SetIOPluseState(TADMotionConn* handle,short crd, short io_num, short IO_State)
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_SetIOPluseState(handle,crd, io_num, IO_State);
}
short __stdcall API_SetIOPluseTrigger(TADMotionConn* handle,short crd, short io_num, short IO_Trigger)
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_SetIOPluseTrigger(handle,crd, io_num,IO_Trigger);
}

short __stdcall API_GetFpgaVersion(TADMotionConn* handle,short &version)
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_GetFpgaVersion(handle,version);
}

//-----------------------------------------------------------
short __stdcall API_SendSpeedIO_Point(TADMotionConn* handle, short crd, short pointNum,SpeedIOPoint p[])
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_SendSpeedIO_Point(handle, crd,pointNum,p);
}

short __stdcall API_SpeedIO_Enable(TADMotionConn* handle, short crd, short enable,short IO_num)
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_SpeedIO_Enable(handle, crd,enable,IO_num);
}

short __stdcall API_SpeedIO_ClearPoint(TADMotionConn* handle, short crd)
{
    if (API_CheckHandle(handle) == false) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }
    return MC_SpeedIO_ClearPoint(handle, crd);
}
//--------------------------------------------------------------------

short __stdcall API_SetLogCallback(TADMotionConn* handle, LogCallback callback, void* userData)
{
    // 注意：这里不能使用g_Log，因为可能还没有设置回调
    if (!API_CheckHandle(handle)) {
        return CMD_API_ERROR_INVALID_HANDLE;
    }

    std::lock_guard<std::mutex> lock(g_handleMapMutex);
    auto it = g_handleMap.find(handle);
    if (it != g_handleMap.end()) {
        it->second->setLogCallback(callback, userData);

        // 设置回调后记录第一条日志
//        if (callback) {
//            g_Log(handle, LOG_INFO, "API_SetLogCallback: 日志回调函数已设置");
//        } else {
//            g_Log(handle, LOG_INFO, "API_SetLogCallback: 日志回调函数已清除");
//        }

        return CMD_SUCCESS;
    }
    return CMD_API_ERROR_INVALID_HANDLE;
}

short __stdcall API_SetLogLevel(TADMotionConn* handle, LogLevel minLevel)
{
    if (!API_CheckHandle(handle)) {
        //g_Log(nullptr, LOG_ERROR, "API_SetLogLevel: 无效的句柄");
        return CMD_API_ERROR_INVALID_HANDLE;
    }

    const char* levelStr = "";
    switch (minLevel) {
        case LOG_DEBUG:   levelStr = "DEBUG"; break;
        case LOG_INFO:    levelStr = "INFO"; break;
        case LOG_WARNING: levelStr = "WARNING"; break;
        case LOG_ERROR:   levelStr = "ERROR"; break;
        case LOG_FATAL:   levelStr = "FATAL"; break;
        default:          levelStr = "UNKNOWN"; break;
    }

    std::lock_guard<std::mutex> lock(g_handleMapMutex);
    auto it = g_handleMap.find(handle);
    if (it != g_handleMap.end()) {
        it->second->setLogLevel(minLevel);
        //g_Log(handle, LOG_INFO, "API_SetLogLevel: 日志级别已设置为 %s", levelStr);
        return CMD_SUCCESS;
    }

    //g_Log(nullptr, LOG_ERROR, "API_SetLogLevel: 句柄未找到");
    return CMD_API_ERROR_INVALID_HANDLE;
}

/**
 * @brief 获取伺服错误码的中文描述信息
 * @param handle 运动控制句柄
 * @param errorCode 伺服错误码（如 0x6B00）
 * @param errorMessage 用户提供的宽字符缓冲区，用于接收错误描述
 * @param bufferSize 缓冲区大小（以wchar_t为单位，包含结尾的null字符）
 * @return CMD_SUCCESS(0): 成功获取错误描述
 *         CMD_API_ERROR_INVALID_HANDLE: 无效句柄
 *         CMD_API_ERROR_POINTER: 空指针参数
 *         CMD_API_ERROR_OUT_RANGE: 缓冲区太小或错误码无效
 */
short __stdcall API_GetServoErrorString(TADMotionConn* handle, short errorCode, wchar_t* errorMessage, int bufferSize)
{
    g_Log(handle, LOG_DEBUG, "API_GetServoErrorString: 获取伺服错误码描述 errorCode=0x%04X, bufferSize=%d",
          errorCode, bufferSize);

    // 1. 验证句柄
    if (API_CheckHandle(handle) == false) {
        g_Log(nullptr, LOG_ERROR, "API_GetServoErrorString: 无效的句柄");
        return CMD_API_ERROR_INVALID_HANDLE;
    }

    // 2. 验证输出缓冲区指针
    if (!errorMessage) {
        return LogAndReturnError(handle, CMD_API_ERROR_POINTER, "API_GetServoErrorString",
                                "错误消息缓冲区指针为空");
    }

    // 3. 验证缓冲区大小
    if (bufferSize <= 0) {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "API_GetServoErrorString",
                                "缓冲区大小无效: %d", bufferSize);
    }

    // 4. 获取错误码对应的描述信息
    const wchar_t* errorDesc = GetMotionErrorString(errorCode);
    if (!errorDesc) {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "API_GetServoErrorString",
                                "无法获取错误码描述: 0x%04X", errorCode);
    }

    // 5. 计算所需的缓冲区大小（包含null终止符）
    size_t descLength = wcslen(errorDesc);
    size_t requiredSize = descLength + 1; // +1 for null terminator

    // 6. 检查缓冲区是否足够大
    if (bufferSize < static_cast<int>(requiredSize)) {
        return LogAndReturnError(handle, CMD_API_ERROR_OUT_RANGE, "API_GetServoErrorString",
                                "缓冲区太小: 需要%zu个字符，提供%d个字符", requiredSize, bufferSize);
    }

    // 7. 安全地复制错误描述到用户缓冲区
    wcsncpy(errorMessage, errorDesc, bufferSize - 1);
    errorMessage[bufferSize - 1] = L'\0'; // 确保字符串以null结尾

    // 8. 记录成功日志
    //g_Log(handle, LOG_INFO, "API_GetServoErrorString: 成功获取错误码描述 errorCode=0x%04X", errorCode);

    return CMD_SUCCESS;
}

