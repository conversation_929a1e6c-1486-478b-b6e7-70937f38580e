#pragma once
#ifndef LOGGER_PROXY_H
#define LOGGER_PROXY_H

#include "admc_pci.h" // For TADMotionConn and LogLevel

/**
 * @brief 全局日志记录代理函数
 *
 * 通过此函数，代码库的任何部分都可以使用TADMotionConn句柄来记录日志，
 * 它会自动将日志请求转发给与该句柄关联的C++ MotionConn包装器实例。
 *
 * @param handle 运动控制句柄。如果为nullptr，日志将被忽略。
 * @param level 日志级别。
 * @param format printf风格的格式化字符串。
 * @param ... 格式化字符串对应的参数。
 */
void g_Log(TADMotionConn* handle, LogLevel level, const char* format, ...);

/**
 * @brief 统一的错误处理函数 - 记录错误日志并返回错误码
 *
 * 此函数用于DLL内部直接返回错误码的情况，会自动根据错误码记录对应的日志信息
 *
 * @param handle 运动控制句柄
 * @param errorCode 错误码
 * @param functionName 发生错误的函数名
 * @param format 额外的错误描述格式字符串（可选）
 * @param ... 格式化字符串对应的参数
 * @return 返回传入的错误码
 */
short LogAndReturnError(TADMotionConn* handle, short errorCode, const char* functionName, const char* format = nullptr, ...);

/**
 * @brief SendCommand错误处理函数 - 区分通讯错误和DSP错误
 *
 * 此函数用于处理SendCommand返回的错误，会根据错误码类型记录对应的日志信息
 *
 * @param handle 运动控制句柄
 * @param errorCode SendCommand返回的错误码
 * @param functionName 调用SendCommand的函数名
 * @param commandInfo 命令相关信息（如参数等）
 * @return 返回传入的错误码
 */
short LogSendCommandError(TADMotionConn* handle, short errorCode, const char* functionName, const char* commandInfo = nullptr);

/**
 * @brief 判断错误码类型的辅助函数
 *
 * @param errorCode 错误码
 * @return 错误类型：0=成功, 1=通讯错误, 2=DSP错误, 3=DLL内部错误, 4=伺服错误
 */
int GetErrorCodeType(short errorCode);

#endif // LOGGER_PROXY_H