#ifndef ADMC_PCI_H
#define ADMC_PCI_H

#ifndef MC_API_EXPORT
#define MC_API_EXPORT
#endif
#include "ADOSType.h"

#if defined(_WIN32) && !defined(__MINGW64__)
#ifdef MC_API_EXPORT
#define MC_API extern "C" __declspec(dllexport)
#else
#define MC_API extern "C" __declspec(dllimport)
#endif
#else
#define MC_API extern "C" __attribute__((visibility("default")))
#endif

#ifdef __cplusplus
class TADMotionConn;
#else
typedef struct TADMotionConn TADMotionConn;
#endif

/******************************************* 日志系统 *******************************************/
/**
 * @brief 日志级别枚举
 */
enum LogLevel {
    LOG_DEBUG,    // 调试信息
    LOG_INFO,     // 一般信息
    LOG_WARNING,  // 警告信息
    LOG_ERROR,    // 错误信息
    LOG_FATAL     // 致命错误
};

/**
 * @brief 日志回调函数指针类型
 * @param level 日志级别
 * @param message 日志消息内容 (UTF-8编码)
 * @param userData 用户在注册回调时提供的自定义数据指针
 */
typedef void(__stdcall* LogCallback)(LogLevel level, const char* message, void* userData);

//接口内单位说明:
// 1. 编码器位置单位：pluse     编码器单位  需要根据编码器分辨率换算成mm
// 2. 速度单位：pluse/ms    
// 3. 加速度单位：pluse/ms^2
// 4. 时间单位：ms

//单位转换举例
// 1. 编码器分辨率：1000 pluse =  1mm = 10^(-3)m    
// 2. 1mm = 1000pluse       1mm/s = 1 pluse/ms    1m/s = 10^3 pluse/ms
// 3. 1mm/s^2 = 10^(-3)pluse/s^2  1 m/s^2 = 1 pluse/s^2    



/******************************************* 卡相关指令 *******************************************/
/**
 * @brief 创建卡    
 * @return 运动控制句柄
 */
MC_API TADMotionConn* __stdcall API_CreateBoard();                                     
/**
 * @brief 删除卡
 * @param handle 运动控制句柄
 */
MC_API void __stdcall API_DeleteBoard(TADMotionConn* handle);                          
/**
 * @brief 打开卡连接
 * @param handle 运动控制句柄
 * @param ip IP地址  *********** / ***********
 * @param port 端口号 6666
 * @return 错误码
 */ 
MC_API short __stdcall API_OpenBoard(TADMotionConn* handle, const char* ip, int port); 
/**
 * @brief 关闭卡连接
 * @param handle 运动控制句柄
 */ 
MC_API short __stdcall API_CloseBoard(TADMotionConn* handle);                          
/**
 * @brief 复位卡
 * @param handle 运动控制句柄
 */
MC_API short __stdcall API_ResetBoard(TADMotionConn* handle);                         

/**
 * @brief 为指定的运动控制句柄设置日志回调函数
 * @param handle 运动控制句柄
 * @param callback 用户实现的日志回调函数。如果为NULL，则禁用回调。
 * @param userData 将在每次调用回调时传递给用户的自定义数据。
 * @return 错误码
 */
MC_API short __stdcall API_SetLogCallback(TADMotionConn* handle, LogCallback callback, void* userData);

/**
 * @brief 设置句柄的最低日志记录级别
 * @param handle 运动控制句柄
 * @param minLevel 要记录的最低日志级别 (例如, 设置为LOG_INFO, 则不会记录LOG_DEBUG级别的日志)
 * @return 错误码
 */
MC_API short __stdcall API_SetLogLevel(TADMotionConn* handle, LogLevel minLevel);

/**
 * @brief 获取伺服错误码的中文描述信息
 * @param handle 运动控制句柄
 * @param errorCode 伺服错误码（如 0x6B00）/dll返回错误值/dsp返回错误值
 * @param errorMessage 用户提供的宽字符缓冲区，用于接收错误描述
 * @param bufferSize 缓冲区大小（以wchar_t为单位，包含结尾的null字符）
 * @return CMD_SUCCESS(0): 成功获取错误描述
 *         CMD_API_ERROR_INVALID_HANDLE: 无效句柄
 *         CMD_API_ERROR_POINTER: 空指针参数
 *         CMD_API_ERROR_OUT_RANGE: 缓冲区太小或错误码无效
 */
MC_API short __stdcall API_GetServoErrorString(TADMotionConn* handle, short errorCode, wchar_t* errorMessage, int bufferSize);

/******************************************* 轴相关指令 *******************************************/
/**
 * @brief 设置轴参数
 * @param handle 运动控制句柄
 * @param crd 坐标系编号 [0 1]
 * @param AxisMap[2] 轴映射 默认[0 1]
 * @param AxisDir[2] 轴方向 [0 1] 默认1
 * @param VelMax[2] 最大速度
 * @param AccMax[2] 最大加速度
 * @param Positive[2] 正方向限位      0=<负限位<Positive[i]
 * @param Negative[2] 负方向限位      0=<Negative[i]<正限位
 * @return 错误码
 */ 
MC_API short __stdcall API_SetAxisPrm(TADMotionConn* handle,
                                      short          crd,     
                                      short*         AxisMap, 
                                      short*         AxisDir, 
                                      int32_t*       VelMax, 
                                      int32_t*       AccMax,  
                                      int32_t*       Positive, 
                                      int32_t*       Negative); 

/**
 * @brief 使能轴
 * @param handle 运动控制句柄
 * @param axis 轴编号 [0 3]
 * @return 错误码
 */
MC_API short __stdcall API_AxisOn(TADMotionConn* handle, short axis); 
/**
 * @brief 使能关
 * @param handle 运动控制句柄
 * @param axis 轴编号 [0 3]
 * @return 错误码
 */
MC_API short __stdcall API_AxisOff(TADMotionConn* handle, short axis);                    
/**
 * @brief 坐标系回零(控制当前坐标系xy回零)
 * @param handle 运动控制句柄
 * @param crd 坐标系编号 [0 1]
 * @return 错误码
 */

MC_API short __stdcall API_GoHome(TADMotionConn* handle, short crd);  

/**
 * @brief 轴回零
 * @param handle 运动控制句柄
 * @param axis 轴编号 [0 3]
 * @return 错误码
 */
MC_API short __stdcall API_AxisGoHome(TADMotionConn* handle, short axis); 

/**
 * @brief 清除轴报警
 * @param handle 运动控制句柄
 * @param axis 轴编号 [0 3]
 * @return 错误码
 */
MC_API short __stdcall API_AxisClearAlarm(TADMotionConn* handle, short axis); 

/**
 * @brief 获取轴反馈位置
 * @param handle 运动控制句柄
 * @param axis 轴编号 [0 3] 
 * @param pPos 轴位置
 * @return 错误码
 */
MC_API short __stdcall API_GetAixsPos(TADMotionConn* handle, short axis, double& pPos);

/**
 * @brief 获取轴指令位置
 * @param handle 运动控制句柄
 * @param axis 轴编号 [0 3]
 * @param pPos 轴位置
 * @return 错误码
 */
MC_API short __stdcall API_GetCmdAixsPos(TADMotionConn* handle, short axis, double& pPos);
/**
 * @brief 获取轴错误码
 * @param handle 运动控制句柄
 * @param axis 轴编号 [0 3]
 * @param ErrorCode 轴错误码
 * @return 错误码
 */
MC_API short __stdcall API_GetErrorCode(TADMotionConn* handle, short axis, unsigned short* ErrorCode);

/**
 * @brief 获取坐标系反馈位置
 * @param handle 运动控制句柄
 * @param crd 坐标系编号 [0 1]
 * @param pPos[2] 坐标系位置
 * @return 错误码
 */
MC_API short __stdcall API_GetCrdPos(TADMotionConn* handle, short crd, double* pPos);
/**
 * @brief 获取坐标系指令位置
 * @param handle 运动控制句柄
 * @param crd 坐标系编号 [0 1]
 * @param pPos[2] 坐标系指令位置
 * @return 错误码
 */
MC_API short __stdcall API_GetCmdCrdPos(TADMotionConn* handle, short crd, double* pPos);
/**
 * @brief 获取轴状态
 * @param handle 运动控制句柄
 * @param axis 轴编号 [0 3]
 * @param Sts 轴状态
 * @return 错误码
 */
MC_API short __stdcall API_GetAxisStatus(TADMotionConn* handle, short axis, short& Sts);

/******************************************* jog运动指令 *******************************************/

/**
 * @brief 设置jog模式
 * @param handle 运动控制句柄
 * @param crd 坐标系编号 [0 1]
 * @return 错误码
 */
MC_API short __stdcall API_SetJogMode(TADMotionConn* handle, short crd);                
/**
* @brief 设置jog参数    
* @param handle 运动控制句柄
* @param crd 坐标系编号 [0 1]
* @param Maxvel 最大速度
* @param acc 加速度
* @param dec 减速度
* @param rate 倍率 (0,100]
* @return 错误码
*/  
MC_API short __stdcall API_SetJogPrm(TADMotionConn* handle, short crd, int32_t Maxvel, int32_t acc, int32_t dec, int32_t rate);  
/**
 * @brief 启动jog运动
 * @param handle 运动控制句柄
 * @param axis 轴编号 [0 3]
 * @param dir 方向 [0 1]
 * @return 错误码
 */
 /**
 axis 范围[0-3]  axis = [0,1]时 对应crd = 0  axis = [2,3]时 对应crd = 1
 dir 范围[-1,0,1]: 0 表示停止  1 表示正方向  -1 表示负方向
*/
MC_API short __stdcall API_JogUpdate(TADMotionConn* handle, short axis, short dir);

/******************************************* 坐标系点位运动 *******************************************/
/**
 * @brief 设置坐标系点位模式
 * @param handle 运动控制句柄
 * @param crd 坐标系编号 [0 1]
 * @return 错误码
 */ 

MC_API short __stdcall API_SetCrdTrapMode(TADMotionConn* handle, short crd);                
/**
 * @brief 设置坐标系点位参数
 * @param handle 运动控制句柄   
 * @param crd 坐标系编号 [0 1]
 * @param posTarget[2]   目标坐标
 * @param velMax         最大速度
 * @param acc            加速度
 * @param rat            倍率 (0-100]
 * @return 错误码
 */ 
MC_API short __stdcall API_SetCrdTrapPrm(TADMotionConn* handle, short crd,double posTarget[2], double velMax,
                                            double acc, short rat);

/**
 * @brief 启动坐标系点位运动
 * @param handle 运动控制句柄
 * @param crd 坐标系编号 [0 1]
 * @return 错误码
 */  
MC_API short __stdcall API_CrdTrapUpdate(TADMotionConn* handle, short crd);

 /******************************************* 轴点位运动 *******************************************/   
/**  
 * @brief 轴点位运动    
 * @param handle 运动控制句柄
 * @param axis 轴编号 [0 3]
 * @return 错误码
 */

MC_API short __stdcall API_SetAxisTrapMode(TADMotionConn* handle, short axis);
/**
 * @brief 设置轴点位参数    
 * @param handle 运动控制句柄
 * @param axis 轴编号 [0 3]
 * @param IncrPos 增量位置 正负号表示方向 数值表示距离  
 * @param velMax 最大速度 
 * @param acc 加速度
 * @param rat 倍率 0-100
 * @return 错误码
 */     

MC_API short __stdcall API_SetAxisTrapPrm(TADMotionConn* handle, short axis, double IncrPos, double velMax, double acc, short rat);
/**
 * @brief 启动轴所在坐标系的点位运动
 * @param handle 运动控制句柄
 * @param axis 轴编号 [0 3]
 * @return 错误码
 */
MC_API short __stdcall API_AxisTrapUpdate(TADMotionConn* handle, short crd); 

/******************************************* 坐标系插补指令 *******************************************/ 
/**
 * @brief 设置坐标系插补参数
 * @param handle 运动控制句柄
 * @param crd 坐标系编号 [0 1]
 * @param synVelMax/synAccMax 坐标系同步速度/加速度
 * @return 错误码
 */ 
MC_API short __stdcall API_SetCrdPrm(TADMotionConn* handle, short crd, double synVelMax, double synAccMax);    

/**
 * @brief 坐标插补开始
 * @param handle 运动控制句柄
 * @param crd 坐标系编号 [0 1]
 * @return 错误码
 */
MC_API short __stdcall API_CrdStart(TADMotionConn* handle, short crd);                        

/**
 * @brief 坐标插补停止
 * @param handle 运动控制句柄
 * @param crd 坐标系编号 [0 1]
 * @return 错误码
 */
MC_API short __stdcall API_CrdStop(TADMotionConn* handle,short crd);
/**
 * @brief 坐标插补暂停
 * @param handle 运动控制句柄
 * @param crd 坐标系编号 [0 1]
 * @return 错误码
 */
MC_API short __stdcall API_CrdPause(TADMotionConn* handle,short crd);



/******************************************* 直线圆弧指令 *******************************************/
/** 
 * @brief 直线指令  
 * @param handle 运动控制句柄
 * @param crd 坐标系号 [0,1]
 * @param x 目标点x坐标
 * @param y 目标点y坐标
 * @param synVel 同步速度
 * @param synAcc 同步加速度
 * @return 错误码
*/
MC_API short __stdcall API_Ln(TADMotionConn* handle, short crd, int32_t x, int32_t y, double synVel, double synAcc);

/** 
 * @brief 三点圆弧指令
 * @param handle 运动控制句柄
 * @param crd 坐标系号 [0,1]
 * @param p1[2] 圆弧起点坐标
 * @param p2[2] 圆弧中间点坐标
 * @param p3[2] 圆弧终点坐标
 * @param radius 圆弧半径
 * @param circleDir 圆弧方向  0 顺时针 1 逆时针
 * @param synVel 同步速度
 * @param synAcc 同步加速度
 * @return 错误码
*/  
MC_API short __stdcall API_ArcXY_3point(TADMotionConn* handle,
                                        short          crd,
                                        int32_t*       p1,
                                        int32_t*       p2,
                                        int32_t*       p3,
                                        double         synVel,
                                        double         synAcc);
/** 
 * @brief 半径圆弧指令
 * @param handle 运动控制句柄
 * @param crd 坐标系号 [0,1]
 * @param x 目标点x坐标
 * @param y 目标点y坐标
 * @param radius 圆弧半径
 * @param circleDir 圆弧方向
 * @param synVel 同步速度
 * @param synAcc 同步加速度
 * @return 错误码
*/  
MC_API short __stdcall API_ArcXYR(TADMotionConn* handle,
                                  short          crd,
                                  int32_t        x,
                                  int32_t        y,
                                  double         radius,
                                  short          circleDir,
                                  double         synVel,
                                  double         synAcc);
/** 
 * @brief 中心圆弧指令
 * @param handle 运动控制句柄
 * @param crd 坐标系号 [0,1]
 * @param x 目标点x坐标
 * @param y 目标点y坐标
 * @param xCenter 圆心x坐标
 * @param yCenter 圆心y坐标 
 * @param circleDir 圆弧方向  0 顺时针 1 逆时针
 * @param synVel 同步速度
 * @param synAcc 同步加速度
 * @return 错误码
*/  
MC_API short __stdcall API_ArcXYC(TADMotionConn* handle,
                                  short          crd,
                                  int32_t        x,
                                  int32_t        y,
                                  double         xCenter,
                                  double         yCenter,
                                  short          circleDir,
                                  double         synVel,
                                  double         synAcc);
/**
 * @brief 前瞻初始化
 * @param handle 运动控制句柄
 * @param crd 坐标系号 [0,1]
 * @param accMax 系统允许最大加速度(一般为所有轴最大加速度的最小值)  accMax<=[axis1MaxAcc,axis2MaxAcc,axis2MaxAcc,...]
 * @param count 前瞻段数  
 * @return 错误码
 */
MC_API short __stdcall API_InitLookAhead(TADMotionConn* handle, short crd, double accMax, short count);
/**
 * @brief 关闭前瞻
 * @param handle 运动控制句柄
 * @param crd 坐标系号 [0,1]
 * @return 错误码
 */
MC_API short __stdcall API_CloseLookAhead(TADMotionConn* handle, short crd);
/******************************************* 普通IO *******************************************/
//普通IO
/**
 * @brief 设置设备输出IO  
 * @param handle 运动控制句柄
 * @param deviceOutput IO输出
 * @return 错误码
 */
MC_API short __stdcall API_SetDeviceOutput(TADMotionConn* handle, int* deviceOutput);

/**
 * @brief 获取设备输出IO
 * @param handle 运动控制句柄
 * @param deviceOutput IO输出
 * @return 错误码
 */
MC_API short __stdcall API_GetDeviceOutput(TADMotionConn* handle, int32_t* deviceOutput);
/**
 * @brief 获取设备输入IO
 * @param handle 运动控制句柄
 * @param deviceInput IO输入
 * @return 错误码
 */
MC_API short __stdcall API_GetDeviceInput(TADMotionConn* handle, int32_t* deviceInput);
/******************************************* 高速IO *******************************************/
//高速IO
/**
 * @brief 设置高速IO参数
 * @param handle 运动控制句柄
 * @param io_num IO编号 [0 1]
 * @param duty 占空比 [0 100]
 * @param period 周期 [0 1000]
 * @return 错误码
 */ 

MC_API short __stdcall API_SetSpeedIOParam(TADMotionConn* handle, short io_num, short duty, short period);
/**
 * @brief 设置高速IO状态
 * @param handle 运动控制句柄
 * @param io_num IO编号 [0 1]
 * @param switch_state 状态 [0 1]
 * @return 错误码
 */
MC_API short __stdcall API_SetSpeedIOState(TADMotionConn* handle, short io_num, short switch_state);
/**
 * @brief 设置高速IO电平
 * @param handle 运动控制句柄
 * @param io_num IO编号 [0 1]
 * @param IO_Enable 电平 [0 1]
 * @return 错误码
 */
//2D点位比较输出(当前安达卡只支持输出到高速IO)
MC_API short __stdcall API_PosCmpEnable(TADMotionConn* handle, short crd, bool bEnable, short error, short frontTime); //// 是否开启位置比较功能
MC_API short __stdcall API_SetPosCmpPoint(
    TADMotionConn* handle, short crd, short seg, double pnt[10240][2], int nPntSize); ////  传输要参与位置比较的点位
MC_API short __stdcall API_ClearCmpPoint(TADMotionConn* handle, short crd);           ////    清理点位
MC_API short __stdcall API_SetPosCmpOutp(TADMotionConn* handle,
    short          crd,
    short            outNum,
    short            outtype,
    long         hlTime_ms,
    long         duty_ms); ////  设置位置比较输出
//--------------
//直接命令操作高速IO电平：
//电平模式支持： io_num ： IO端口 0/1, 返回值 = 0 ，代表运控已成功传输命令到DSP
//电平模式有效
MC_API short __stdcall API_SetIOPluseEnable(TADMotionConn* handle,short crd, short io_num, short IO_Enable);
//直接输出高或低电平。
MC_API short __stdcall API_SetIOPluseState(TADMotionConn* handle,short crd, short io_num, short IO_State);
//直接翻转电平
MC_API short __stdcall API_SetIOPluseTrigger(TADMotionConn* handle,short crd, short io_num, short IO_Trigger);
//取FPGA版本，version = 260 ，该函数成功上面三个函数才有效。否则无效。
MC_API short __stdcall API_GetFpgaVersion(TADMotionConn* handle,short &version);
//--------------
/**
 * @brief 速度IO点结构体SpeedIOPoint
 * isOpen:是否开阀   openStyle：开阀方式  x/y：胶阀目标点
 */
struct SpeedIOPoint;

MC_API short __stdcall API_SendSpeedIO_Point(TADMotionConn* handle, short crd, short pointNum, SpeedIOPoint p[]);
MC_API short __stdcall API_SpeedIO_Enable(TADMotionConn* handle, short crd, short enable, short IO_num);
MC_API short __stdcall API_SpeedIO_ClearPoint(TADMotionConn* handle, short crd);

MC_API short __stdcall API_DebugModelOption(TADMotionConn* handle, bool);

#endif
