﻿#pragma once
#ifndef ADMC_H
#define ADMC_H

#include "admc_info.h"
#include "ADOSType.h"
class TADMotionConn;

/*
直接与运控交互指令执行
*/

// short MC_LoadConfig(); //加载配置信息
// short MC_SaveConfig(); //保存配置信息

short MC_Open(TADMotionConn* handle, const char* ip, int port);
short MC_Close(TADMotionConn* handle);

short MC_Reset(TADMotionConn* handle);
short MC_SetAxisPrm(TADMotionConn* handle,
                    short          crd,
                    short*         forAxisNum,
                    short*         forAxisDir,
                    int32_t*       forVelMax,
                    int32_t*       forAccMax,
                    int32_t*       Positive,
                    int32_t*       Negative);

short MC_ClrSts(TADMotionConn* handle, short axis, short count); //清除指定轴报警、限位、误差越限等状态
short MC_GetSts(TADMotionConn* handle, short axis, int32_t* pSts, short count); //获得指定轴报警、限位、误差越限等状态

short MC_AxisEnable(TADMotionConn* handle, short crd, short axis, short operate);       //轴使能
short MC_AlarmEnable(TADMotionConn* handle, short axis, short operate);                 //检查轴的报警信号使能
short MC_LmtsEnable(TADMotionConn* handle, short axis, short limitType, short operate); //限位使能

short MC_SetSoftLimit(TADMotionConn* handle, short axis, int32_t positive, int32_t negative);     //设置目标轴软限位
short MC_GetSoftLimit(TADMotionConn* handle, short axis, int32_t* pPositive, int32_t* pNegative); //获取目标轴软限位

short MC_Stop(TADMotionConn* handle, int32_t mask, int32_t option);    //停止 mask轴号   option停止类型
short MC_ZeroPos(TADMotionConn* handle, short crd);                    //回零
short MC_AxisHome(TADMotionConn* handle, short crd, short axis);       //单轴回零
short MC_AxisClearAlarm(TADMotionConn* handle, short crd, short axis); //单轴清除报警
//指令ID：获取错误代码
short MC_GetErrorCode(TADMotionConn* handle,short crd,short axis,short *ErrorCode);//返回ErrorCode{CMD=AXIS_GET_ERRORCODE,ErrorCode(int16_t)}

short MC_RECORD_ZERO_POS(TADMotionConn* handle, short crd);            //设置零位置

short MC_GetAxisInfo(TADMotionConn* handle, short axis, short type, double* pValue, short count); //获取轴信息
short MC_GetCountInfo(
    TADMotionConn* handle, short encoder, short type, double* pValue, short count); //获取轴编码器位置速度加速度
short MC_GetPrfMode(TADMotionConn* handle, short profile, short type, short* pValue, short count); //获得对应轴的模式
/***点位模式***/
short MC_SetPrfPos(TADMotionConn* handle, short crd, int32_t* prfPos); //设置点位位置
short MC_CrdPrfTrap(TADMotionConn* handle, short crd);                 //设置对应虚轴为点位工作模式
short MC_SetCrdTrapPrm(TADMotionConn* handle, short profile, TTrapPrm* pPrm); //设置对应虚轴在点位模式下的参数动更新  修改
short MC_CrdTrapUpdate(TADMotionConn* handle, short crd);
// Axis trap
short MC_AxisPrfTrap(TADMotionConn* handle, short crd, short axis);
short MC_SetAixsTrapPrm(TADMotionConn* handle, short crd, short axis, double IncrPos, double velMax, double acc, short rat);
short MC_AxisTrapUpdate(TADMotionConn* handle, short crd);

/*JOG模式*/
short SetJogMode(TADMotionConn* handle, short crd);                  //设置对应实轴为jog模式
short MC_SetJogPrm(TADMotionConn* handle, short crd, int32_t Maxvel, int32_t acc, int32_t dec, int32_t rate); //设置对应实轴的参数
short MC_JOGUpdata(TADMotionConn* handle, short crd, short mask); // mask = 1 2 3 4 x+ x- y+ y-

/*坐标系*/
short MC_SetZeroPos(TADMotionConn* handle, short crd);                  //设置坐标系零点位置
short MC_SetCrdPrm(TADMotionConn* handle, short crd, TCrdPrm* pCrdPrm); //设置坐标系参数
short MC_GetCrdPrm(TADMotionConn* handle, short crd, TCrdPrm* pCrdPrm); //获取坐标系参数
short MC_CrdClear(TADMotionConn* handle, short crd);
short MC_CrdStart(TADMotionConn* handle, short crd);                               //坐标系启动
short MC_CrdStop(TADMotionConn* handle, short crd); //立即停止坐标系运动
short MC_CrdPause(TADMotionConn* handle, short crd);                         //数据段暂停
short MC_GetCrdPos(TADMotionConn* handle, short crd, double* pPos, short count);   //获得坐标系内各轴的位置
short MC_GetCrdStatus(TADMotionConn* handle, short crd, short* pRun, short* pCrdComplete, short* pCrdFifo0Pause);
short MC_GetfbCrdPos(TADMotionConn* handle, short crd, double* pPos, short count);   //获得坐标系内各轴的反馈位置


short MC_SetWorkCrd(TADMotionConn* handle,short crd, double XWorkhome,double YWorkhome);
short MC_SethomeDir(TADMotionConn* handle,short crd ,short XhomeDir,short YhomeDir);

short MC_CrdData(TADMotionConn* handle, short crd, TCrdData* pCrdData);
short MC_GetCrdSpace(TADMotionConn* handle, short crd, int32_t* pSpace);
short MC_GetCrdSynVel(TADMotionConn* handle, short crd, double* pSynVel);
short MC_ReturnFifo0PausePos(TADMotionConn* handle, short crd, double synVel, double synAcc);

//前瞻初始化函数
short MC_InitLookAhead(TADMotionConn* handle, short crd, double accMax, TCrdData* pLookAheadBuf, short count);
short MC_CloseLookAhead(TADMotionConn* handle, short crd);

short MC_CrdLn(TADMotionConn* handle,
               short          lnType,
               short          isG0,
               short          crd,
               int32_t        x,
               int32_t        y,
               int32_t        z,
               int32_t        a,
               int32_t        b,
               double         synVel,
               double         synAcc,
               double         velEnd);
short MC_CrdArcR(TADMotionConn* handle,
                 short          arcPlat,
                 short          crd,
                 int32_t        x,
                 int32_t        y,
                 int32_t        z,
                 double         radius,
                 short          circleDir,
                 double         synVel,
                 double         synAcc,
                 double         velEnd);
short MC_CrdArcC(TADMotionConn* handle,
                 short          arcPlat,
                 short          crd,
                 int32_t        x,
                 int32_t        y,
                 int32_t        z,
                 double         xCenter,
                 double         yCenter,
                 double         zCenter,
                 short          circleDir,
                 double         synVel,
                 double         synAcc,
                 double         velEnd);
short MC_CrdArcXYZ(TADMotionConn* handle,
                   short          crd,
                   int32_t        x,
                   int32_t        y,
                   int32_t        z,
                   double         interX,
                   double         interY,
                   double         interZ,
                   double         synVel,
                   double         synAcc,
                   double         velEnd);
short MC_CrdArc3Point(TADMotionConn* handle,
                      short          arcPlat,
                      short          crd,
                      int32_t*       p1,
                      int32_t*       p2,
                      int32_t*       p3,
                      double         synVel,
                      double         synAcc,
                      double         velEnd);

short MC_JumpXYZ(TADMotionConn* handle,
                 short          crd,
                 int32_t        x,
                 int32_t        y,
                 int32_t        z,
                 int32_t        h1,
                 int32_t        h2,
                 double         zVel,
                 double         zAcc,
                 double         synVel,
                 double         synAcc);
// BUFIO函数操作
short MC_BufIO(TADMotionConn* handle, short crd, unsigned short doType, unsigned short doMask, unsigned short doValue);
//short MC_BufDelay(TADMotionConn* handle, short crd, unsigned short delayTime);
//short MC_BufDA(TADMotionConn* handle, short crd, short chn, short daValue);
//short MC_BufLmtsOn(TADMotionConn* handle, short crd, short axis, short limitType);
//short MC_BufLmtsOff(TADMotionConn* handle, short crd, short axis, short limitType);
short MC_BufSetStopIo(
    TADMotionConn* handle, short crd, short axis, short stopType, short inputType, short inputIndex);

short MC_BufGear(TADMotionConn* handle, short crd, short gearAxis, int32_t pos);
//设置旋转坐标轴在工件坐标系位置
// short MC_SetCrdRotatePrm(TADMotionConn* handle,short crd, double *pos, short count, double errorBias);
//五轴直线插补
// short MC_FiveAxis_XYZAC(TADMotionConn* handle,short crd, double X, double Y, double Z, double A, double C, double synVel,
// double synAcc, double velEnd);

short MC_SetPosCmpPoint(TADMotionConn* handle, short crd, short seg, double pnt[10240][2], int nPntSize);

short MC_SetPosCmpOutp(TADMotionConn* handle,
    short          crd,
    short            outNum,
    short            outtype,
    long         hlTime_ms,
    long         duty_ms);
short MC_PosCmpEnable(TADMotionConn* handle, short crd, bool bEnable, short error, short frontTime);
short MC_ClearCmpPoint(TADMotionConn* handle, short crd);
#endif
