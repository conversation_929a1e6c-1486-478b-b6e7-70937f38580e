/**
 * @file motion_error_codes.cpp
 * @brief ADMotion运动控制库错误码信息实现
 * @version 2.0
 * @date 2024-12-19
 */

#include "motion_error_codes.h"
#include <map>
#include <string>
#include <iostream>
#include <iomanip>

//=============================================================================
// 错误码中文描述信息映射表（仅保留实际使用的错误码）
//=============================================================================
static const std::map<int, std::wstring> g_errorMessages = {
    // 成功状态
    {MOTION_SUCCESS, L"操作成功"},

    // 通信和连接错误
    {1, L"打开连接失败"},  // CMD_API_ERROR_OPEN
    {MOTION_ERROR_COMM_TIMEOUT, L"通信超时"},
    {MOTION_ERROR_COMM_FORMAT, L"数据格式错误"},
    {MOTION_ERROR_COMM_CONNECTION, L"连接错误"},

    // 参数和输入验证错误
    {MOTION_ERROR_PARAM_OUT_RANGE, L"参数超出有效范围"},
    {MOTION_ERROR_PARAM_TYPE, L"参数类型错误"},
    {MOTION_ERROR_PARAM_POINTER, L"指针参数无效"},
    {MOTION_ERROR_PARAM_INVALID, L"参数值无效"},
    {MOTION_ERROR_PARAM_HANDLE_INVALID, L"句柄无效"},

    // 坐标系和插补错误
    {40, L"坐标系FIFO缓冲区满"},  // CMD_API_ERROR_CRD_FIFO_FULL
    {MOTION_ERROR_CRD_LINE_ZERO_LENGTH, L"插补直线长度为零"},
    {MOTION_ERROR_CRD_ARC_CENTER, L"圆弧圆心坐标错误"},
    {MOTION_ERROR_CRD_ARC_END_POSITION, L"圆弧终点位置错误"},
    {MOTION_ERROR_CRD_ARC_RADIUS, L"圆弧半径错误"},
    {MOTION_ERROR_CRD_ARC3D_COLLINEAR, L"空间圆弧三点共线"},
    {MOTION_ERROR_CRD_ARC3D_RADIUS_SMALL, L"空间圆弧半径过小"},

    // 导出接口错误码（ADMC_ERROR_*系列）
    {ADMC_ERROR_OPEN, L"打开设备失败"},
    {ADMC_ERROR_OUT_RANGE, L"参数超出范围"},
    {ADMC_ERROR_TYPE, L"参数类型错误"},
    {ADMC_ERROR_CRD_DEMESION, L"坐标系维度错误"},
    {ADMC_ERROR_CRD_DATA, L"坐标系数据错误"},
    {ADMC_ERROR_CRD_MODE, L"坐标系模式错误"},
    {ADMC_ERROR_CRD_RUN, L"坐标系运行错误"},
    {ADMC_ERROR_CRD_STOP, L"坐标系停止错误"},
    {ADMC_ERROR_CRD_PAUSE, L"坐标系暂停错误"},
    {ADMC_ERROR_CRD_DATA2, L"坐标系数据错误2"},
    {ADMC_ERROR_CRD_BUF_FULL, L"坐标系缓冲区满"},
    {ADMC_ERROR_CRD_BUF_EMPTY, L"坐标系缓冲区空"},
    {ADMC_ERROR_CRD_BUF_OVERFLOW, L"坐标系缓冲区溢出"},
    {ADMC_ERROR_CRD_BUF_UNDERFLOW, L"坐标系缓冲区下溢"},
    {ADMC_ERROR_CRD_BUF_DATA, L"坐标系缓冲区数据错误"},
    {ADMC_ERROR_CRD_BUF_DATA_TYPE, L"坐标系缓冲区数据类型错误"},
    {ADMC_ERROR_CRD_BUF_DATA_SUB_TYPE, L"坐标系缓冲区数据子类型错误"},

    // DSP底层错误 (负数区间) - 保留所有DSP错误码
    {MOTION_ERROR_DSP_LOAD, L"DSP数据加载错误"},
    {MOTION_ERROR_DSP_PARSE, L"DSP解析错误"},
    {MOTION_ERROR_DSP_ADD_RESULT, L"DSP添加结果错误"},
    {MOTION_ERROR_DSP_PARAMETER, L"DSP参数错误"},
    {MOTION_ERROR_DSP_AXIS_NOT_ENABLED, L"DSP轴未使能"},
    {MOTION_ERROR_DSP_AXIS_ENABLED, L"DSP轴已使能"},
    {MOTION_ERROR_DSP_AXIS_RUNNING, L"DSP轴正在运动"},
    {MOTION_ERROR_DSP_PROFILE_RUNNING, L"DSP规划正在运动"},
    {MOTION_ERROR_DSP_AXIS_MAP, L"DSP轴映射错误"},
    {MOTION_ERROR_DSP_AXIS_STATUS, L"DSP轴状态异常"},
    {MOTION_ERROR_DSP_PROFILE_MODE, L"DSP规划模式错误"},
    {MOTION_ERROR_DSP_HOOK, L"DSP有挂接"},
    {MOTION_ERROR_DSP_PROFILE_MODE_HOME, L"DSP规划处于回零模式"},
    {MOTION_ERROR_DSP_UNKNOWN, L"DSP未知指令"},
    {MOTION_ERROR_DSP_CRD_HOST_FIFO_RUN, L"DSP插补主FIFO正在运行"},
    {MOTION_ERROR_DSP_CRD_FIFO1_RUN, L"DSP插补FIFO1正在运行"},
    {MOTION_ERROR_DSP_CRD_AXIS_MAP_SAME, L"DSP多个轴映射相同"},
    {MOTION_ERROR_DSP_CRD_FIFO_OVERFLOW, L"DSP缓存区溢出"},
    {MOTION_ERROR_DSP_HOME_LIMIT_MAP, L"DSP回零限位映射异常"},
    {MOTION_ERROR_DSP_COMPARE_NOT_CONFIG, L"DSP位置比较未配置"},
    {MOTION_ERROR_DSP_COMPARE_RUNNING, L"DSP位置比较正在运行"},
    {MOTION_ERROR_DSP_AUTO_TRIG_CRD_NO_CFG, L"DSP自动触发坐标系未配置"}
};

//=============================================================================
// 伺服错误码中文描述信息映射表 - 根据实际伺服驱动器错误码更新
//=============================================================================
static const std::map<int, std::wstring> g_servoErrorMessages = {
    // 系统参数类错误 (0x0101-0x0136)
    {MOTION_ERROR_SERVO_SYS_PARAM_0101, L"系统参数异常"},
    {MOTION_ERROR_SERVO_SYS_PARAM_0222, L"系统参数异常"},
    {MOTION_ERROR_SERVO_SYS_PARAM_0333, L"系统参数异常"},
    {MOTION_ERROR_SERVO_LOGIC_CONFIG, L"逻辑配置故障"},
    {MOTION_ERROR_SERVO_FPGA_MCU_MISMATCH, L"FPGA和MCU产品型号不匹配"},
    {MOTION_ERROR_SERVO_FPGA_INTERRUPT, L"FPGA中断发送故障 运行超时"},
    {MOTION_ERROR_SERVO_SYS_PARAM_RESET, L"系统参数异常 不可复位,需恢复出厂参数"},
    {MOTION_ERROR_SERVO_PARAM_STORAGE, L"参数存储故障"},
    {MOTION_ERROR_SERVO_FACTORY_PARAM, L"厂家参数异常"},
    {MOTION_ERROR_SERVO_ENCODER_MISMATCH, L"产品匹配故障,无对应的编码器"},
    {MOTION_ERROR_SERVO_MOTOR_PARAM_MISMATCH, L"设置绝对位置功能时电机参数不匹配"},
    {MOTION_ERROR_SERVO_MOTOR_ROM_DATA, L"电机ROM中数据校验错误或未存入参数"},

    // 过流和电流类错误 (0x0200-0x0234, 0x2207)
    {MOTION_ERROR_SERVO_SOFTWARE_OVERCURRENT, L"软件过流故障"},
    {MOTION_ERROR_SERVO_HARDWARE_OVERCURRENT, L"硬件过流故障"},
    {MOTION_ERROR_SERVO_MCU_TORQUE_TIMEOUT, L"MCU未及时更新转矩指令引起的超时错误"},
    {MOTION_ERROR_SERVO_OUTPUT_SHORT_GND, L"输出对地短路"},
    {MOTION_ERROR_SERVO_UVW_WIRE_ERROR, L"UVW三相接错线后的飞车报警"},
    {MOTION_ERROR_SERVO_DQ_CURRENT_OVERFLOW, L"D/Q电流溢出"},

    // 电压类错误 (0x0430, 0x2400, 0x2410, 0x6430)
    {MOTION_ERROR_SERVO_CTRL_POWER_UNDER, L"控制电源欠电压"},
    {MOTION_ERROR_SERVO_OVERVOLTAGE, L"过电压"},
    {MOTION_ERROR_SERVO_UNDERVOLTAGE, L"欠电压"},
    {0x2430,L"欠电压"},
    {MOTION_ERROR_SERVO_CTRL_POWER_UNDER2, L"控制电源欠电压"},

    // AD采样类错误 (0x0834, 0x0835, 0x6834, 0x6835)
    {MOTION_ERROR_SERVO_DSP_AD_OVERVOLT, L"DSP的AD采样过压故障"},
    {MOTION_ERROR_SERVO_FPGA_AD_FAULT, L"FPGA报出的AD采样故障"},
    {MOTION_ERROR_SERVO_DSP_AD_OVERVOLT2, L"DSP的AD采样过压故障"},
    {MOTION_ERROR_SERVO_FPGA_AD_FAULT2, L"FPGA报出的AD采样故障"},

    // 编码器类错误 (0x0740, 0x0A33-0x0A35, 0x6731-0x6745, 0x2770)
    {MOTION_ERROR_SERVO_ENCODER_Z_NOISE, L"编码器Z干扰故障"},
    {MOTION_ERROR_SERVO_ENCODER_PARAM_ERR, L"编码器参数异常"},
    {MOTION_ERROR_SERVO_ENCODER_CHECKSUM_ERR, L"编码器回送校验异常"},
    {MOTION_ERROR_SERVO_ENCODER_Z_BREAK, L"Z断线"},
    {MOTION_ERROR_SERVO_ENCODER_BATTERY, L"编码器电池失效"},
    {MOTION_ERROR_SERVO_ENCODER_MULTI_COUNT, L"编码器多圈计数错误"},
    {MOTION_ERROR_SERVO_ENCODER_MULTI_OVER, L"编码器多圈计数器溢出"},
    {MOTION_ERROR_SERVO_ENCODER_Z_NOISE2, L"编码器Z干扰故障"},
    {MOTION_ERROR_SERVO_EXT_ENCODER_SCALE, L"外部编码器标尺故障"},

    // 功能分配类错误 (0x2130, 0x2131)
    {MOTION_ERROR_SERVO_DI_FUNC_ASSIGN, L"DI功能分配故障(除了重复分配故障外，还包括分配功能超限，手轮中断定长分配功能不合理等故障)"},
    {MOTION_ERROR_SERVO_DO_FUNC_ASSIGN, L"DO功能分配故障"},

    // 速度和运动类错误 (0x2500, 0x6500)
    {MOTION_ERROR_SERVO_SPEED_OVER_MAX, L"速度超过最高转速"},
    {MOTION_ERROR_SERVO_SPEED_OVER_MAX2, L"速度超过最高转速"},

    // 辨识类错误 (0x2600, 0x2602, 0xEA40)
    {MOTION_ERROR_SERVO_INERTIA_ID_FAIL, L"离线惯量辨识失败"},
    {MOTION_ERROR_SERVO_ANGLE_ID_FAIL, L"角度辨识失败"},
    {MOTION_ERROR_SERVO_PARAM_ID_FAIL, L"参数辨识失败"},

    // 伺服控制类错误 (0x6121, 0x6300)
    {MOTION_ERROR_SERVO_ON_CMD_INVALID, L"伺服ON指令无效故障"},
    {MOTION_ERROR_SERVO_STO_SIGNAL_PROTECT, L"STO信号输入保护"},

    // 电源和继电器类错误 (0x6420, 0x6421)
    {MOTION_ERROR_SERVO_POWER_LINE_MISSING, L"电源线缺相"},
    {MOTION_ERROR_SERVO_RELAY_FAULT, L"继电器故障"},

    // 过载和温度类错误 (0x6610, 0x6620, 0x6630, 0x6650, 0x6660, 0xE909)
    {MOTION_ERROR_SERVO_DRIVER_OVERLOAD, L"驱动器过载"},
    {MOTION_ERROR_SERVO_MOTOR_OVERLOAD, L"电机过载"},
    {MOTION_ERROR_SERVO_MOTOR_STALL_HEAT, L"电机堵转过热保护"},
    {MOTION_ERROR_SERVO_HEATSINK_OVERHEAT, L"散热片温度过高(来自电流累加信号)"},
    {MOTION_ERROR_SERVO_MOTOR_OVERHEAT, L"电机温度过高"},
    {MOTION_ERROR_SERVO_MOTOR_OVERLOAD_WARN, L"电机过载警告"},

    // 脉冲输出类错误 (0x6510, 0xE110)
    {MOTION_ERROR_SERVO_PULSE_OUT_OVERSPEED, L"分频脉冲输出过速"},
    {MOTION_ERROR_SERVO_PULSE_OUT_SET_ERR, L"分频脉冲输出设定故障"},

    // 抱闸类错误 (0x6625, 0x6626)
    {MOTION_ERROR_SERVO_BRAKE_ABNORMAL_CLOSE, L"抱闸非正常关闭"},
    {MOTION_ERROR_SERVO_BRAKE_ABNORMAL_OPEN, L"抱闸非正常打开"},

    // 位置类错误 (0x6B00-0x6B06)
    {MOTION_ERROR_SERVO_POSITION_ERROR, L"位置偏差过大"},
    {MOTION_ERROR_SERVO_POSITION_CMD_ERR, L"位置指令输入异常"},
    {MOTION_ERROR_SERVO_FULL_CLOSE_POS_ERR, L"全闭环位置偏差过大"},
    {MOTION_ERROR_SERVO_ELEC_GEAR_SET_ERR, L"电子齿轮设定错误"},
    {MOTION_ERROR_SERVO_FULL_CLOSE_SWITCH, L"全闭环和多段位置绝对模式混用时不能内外切换"},
    {MOTION_ERROR_SERVO_GANTRY_SYNC_POS_ERR, L"龙门同步位置偏差过大"},
    {MOTION_ERROR_SERVO_GANTRY_SYNC_TOR_ERR, L"龙门同步转矩偏差过大"},

    // 制动电阻类错误 (0xE920-0xE922)
    {MOTION_ERROR_SERVO_REGEN_RESIST_OVERLOAD, L"再生制动电阻过载"},
    {MOTION_ERROR_SERVO_REGEN_RESIST_OVERCUR, L"再生制动电阻过流"},
    {MOTION_ERROR_SERVO_EXT_REGEN_RESIST_SMALL, L"外接再生制动电阻阻值过小"},

    // 回零类错误 (0xE601)
    {MOTION_ERROR_SERVO_HOME_RETURN_TIMEOUT, L"原点复归回零超时错误"},

    // 其他故障类错误 (0xE831, 0xE939)
    {MOTION_ERROR_SERVO_AI_ZERO_OFFSET_LARGE, L"AI零偏过大"},
    {MOTION_ERROR_SERVO_MOTOR_POWER_BREAK, L"电机动力线断线"},

    // 警告类错误 (0xE900, 0xE941-0xE942, 0xE950, 0xE952, 0xE980, 0xE990, 0xE994-0xE998)
    {MOTION_ERROR_SERVO_EMERGENCY_STOP_WARN, L"紧急停机警告"},
    {MOTION_ERROR_SERVO_PARAM_CHANGE_REBOOT, L"需要重新接通电源的参数变更"},
    {MOTION_ERROR_SERVO_PARAM_STORE_FREQ, L"参数存储频繁警告"},
    {MOTION_ERROR_SERVO_POSITIVE_LIMIT_WARN, L"正向超程警告"},
    {MOTION_ERROR_SERVO_NEGATIVE_LIMIT_WARN, L"负向超程警告"},
    {MOTION_ERROR_SERVO_ENCODER_ALG_WARN, L"编码器算法异常警告"},
    {MOTION_ERROR_SERVO_POWER_PHASE_WARN, L"电源缺相警告"},
    {MOTION_ERROR_SERVO_CANLINK_ADDR_CONFLICT, L"CANLINK地址冲突"},
    {MOTION_ERROR_SERVO_BUS_RECOVER_WARN, L"总线恢复警告"},
    {MOTION_ERROR_SERVO_BUS_PASSIVE_ERR_WARN, L"总线被动错误警告"},
    {MOTION_ERROR_SERVO_POS_CMD_FEEDBACK_LIMIT, L"位置指令或位置反馈达到极限值"},
    {MOTION_ERROR_SERVO_OBJ_DICT_VALUE_ERR, L"对象字典数值设置错误"}
};

//=============================================================================
// 错误信息函数实现
//=============================================================================

/**
 * @brief 获取错误码对应的中文描述信息
 * @param errorCode 错误码
 * @return 错误描述字符串
 */
const wchar_t* GetMotionErrorString(int errorCode) {
    // 调试输出
    #ifdef DEBUG_ERROR_CODES
    std::cout << "查找错误码: " << errorCode << " (0x" << std::hex << errorCode << ")" << std::endl;
    #endif

    // 首先在主错误码表中查找
    auto it = g_errorMessages.find(errorCode);
    if (it != g_errorMessages.end()) {
        #ifdef DEBUG_ERROR_CODES
        std::cout << "在主错误码表中找到" << std::endl;
        #endif
        return it->second.c_str();
    }

    // 然后在伺服错误码表中查找
    auto servoIt = g_servoErrorMessages.find(errorCode);
    if (servoIt != g_servoErrorMessages.end()) {
        #ifdef DEBUG_ERROR_CODES
        std::cout << "在伺服错误码表中找到" << std::endl;
        #endif
        return servoIt->second.c_str();
    }

    // 如果都没找到，返回默认错误信息
    #ifdef DEBUG_ERROR_CODES
    std::cout << "未找到错误码，返回默认信息" << std::endl;
    #endif

    // 使用thread_local确保线程安全，每次调用都更新内容
    static thread_local std::wstring unknownError;
    unknownError = L"未知错误,错误码" + std::to_wstring(errorCode);
    return unknownError.c_str();
}

/**
 * @brief 获取错误码对应的英文描述信息
 * @param errorCode 错误码
 * @return 错误描述字符串
 */
const char* GetMotionErrorStringA(int errorCode) {
    // 简化实现，将中文转换为英文（实际项目中可以建立英文映射表）
    static std::string errorStr;
    const wchar_t* wstr = GetMotionErrorString(errorCode);
    
    // 简单的转换，实际应该使用更完善的转换方法
    errorStr = "Error Code: " + std::to_string(errorCode);
    return errorStr.c_str();
}
